#!/usr/bin/env python3
"""
Test script for metrics and quality scoring system
"""

import asyncio
import json
import time
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_metrics_endpoint():
    """Test the /metrics endpoint"""
    print("🧪 Testing Metrics Endpoint")
    print("=" * 50)
    
    try:
        import httpx
        
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8000/metrics")
            
            if response.status_code == 200:
                print("✅ Metrics endpoint is accessible")
                
                content = response.text
                print(f"📊 Metrics content length: {len(content)} characters")
                
                # Check for expected metrics
                expected_metrics = [
                    "cache_hits_total",
                    "cache_misses_total", 
                    "api_calls_total",
                    "api_errors_total",
                    "response_time_ms",
                    "job_quality_score"
                ]
                
                found_metrics = []
                for metric in expected_metrics:
                    if metric in content:
                        found_metrics.append(metric)
                        print(f"   ✅ Found metric: {metric}")
                    else:
                        print(f"   ❌ Missing metric: {metric}")
                
                print(f"📈 Found {len(found_metrics)}/{len(expected_metrics)} expected metrics")
                
                # Show sample metrics
                lines = content.split('\n')[:20]  # First 20 lines
                print("\n📋 Sample metrics:")
                for line in lines:
                    if line and not line.startswith('#'):
                        print(f"   {line}")
                
            else:
                print(f"❌ Metrics endpoint failed: {response.status_code}")
                print(f"Response: {response.text}")
                
    except Exception as e:
        print(f"❌ Error testing metrics endpoint: {e}")

async def test_quality_scoring():
    """Test job quality scoring"""
    print("\n🧪 Testing Job Quality Scoring")
    print("=" * 50)
    
    try:
        from quality import score, score_and_sort_jobs, get_quality_distribution
        
        # Test jobs with different quality factors
        test_jobs = [
            {
                "id": "1",
                "title": "Senior Software Engineer",
                "company": "TechCorp",
                "description": "We are looking for a senior software engineer to join our team. This role involves developing scalable web applications, working with modern technologies, and collaborating with cross-functional teams. Requirements include 5+ years of experience, proficiency in Python/JavaScript, and strong problem-solving skills. We offer competitive salary, health benefits, flexible work arrangements, and professional development opportunities.",
                "salary": "$120,000 - $150,000",
                "company_logo": "https://example.com/logo.png",
                "datePosted": "2024-01-15T10:00:00Z",
                "daysOld": 2
            },
            {
                "id": "2", 
                "title": "Junior Developer",
                "company": "StartupXYZ",
                "description": "Entry level position.",
                "datePosted": "2024-01-01T10:00:00Z",
                "daysOld": 25
            },
            {
                "id": "3",
                "title": "Data Scientist",
                "company": "DataCorp",
                "description": "Join our data science team to work on machine learning projects, analyze large datasets, and build predictive models. We're looking for someone with experience in Python, SQL, and statistical analysis. This is a remote-friendly position with excellent benefits including health insurance, 401k matching, and unlimited PTO. The ideal candidate will have a PhD in a quantitative field and 3+ years of industry experience.",
                "salary": "$130,000",
                "company_logo": "https://datacorp.com/logo.png",
                "datePosted": "2024-01-20T10:00:00Z",
                "daysOld": 1
            },
            {
                "id": "4",
                "title": "Old Job Posting",
                "company": "OldCorp", 
                "description": "Very old job posting with minimal details.",
                "datePosted": "2023-12-01T10:00:00Z",
                "daysOld": 45
            }
        ]
        
        print("📊 Testing individual job scores:")
        for job in test_jobs:
            quality_score = score(job)
            print(f"   Job {job['id']}: {quality_score:.3f} - {job['title']}")
        
        # Test sorting
        print("\n🔄 Testing job sorting:")
        sorted_jobs = score_and_sort_jobs(test_jobs.copy())
        
        print("Jobs sorted by quality:")
        for i, job in enumerate(sorted_jobs, 1):
            print(f"   {i}. {job['title']} (Score: {job['qualityScore']:.3f})")
        
        # Test quality distribution
        print("\n📈 Testing quality distribution:")
        distribution = get_quality_distribution(sorted_jobs)
        print(f"   Count: {distribution['count']}")
        print(f"   Average: {distribution['average']}")
        print(f"   Min: {distribution['min']}")
        print(f"   Max: {distribution['max']}")
        print("   Distribution:")
        for bucket, count in distribution['distribution'].items():
            print(f"     {bucket}: {count} jobs")
        
        print("✅ Quality scoring tests completed")
        
    except Exception as e:
        print(f"❌ Error testing quality scoring: {e}")
        import traceback
        traceback.print_exc()

async def test_prometheus_metrics():
    """Test Prometheus metrics recording"""
    print("\n🧪 Testing Prometheus Metrics Recording")
    print("=" * 50)
    
    try:
        from metrics import (
            record_cache_hit, record_cache_miss, record_api_call, 
            record_api_error, record_job_quality, track_response_time
        )
        
        # Test cache metrics
        print("📊 Recording test cache metrics...")
        record_cache_hit("redis")
        record_cache_hit("redis")
        record_cache_miss("redis")
        print("   ✅ Cache metrics recorded")
        
        # Test API metrics
        print("📊 Recording test API metrics...")
        record_api_call("jooble", "success")
        record_api_call("adzuna", "success")
        record_api_error("jooble", "timeout")
        print("   ✅ API metrics recorded")
        
        # Test quality metrics
        print("📊 Recording test quality metrics...")
        record_job_quality(0.8)
        record_job_quality(0.6)
        record_job_quality(0.9)
        print("   ✅ Quality metrics recorded")
        
        # Test response time tracking
        print("📊 Testing response time tracking...")
        with track_response_time("test_endpoint"):
            await asyncio.sleep(0.1)  # Simulate work
        print("   ✅ Response time tracked")
        
        print("✅ Prometheus metrics tests completed")
        
    except Exception as e:
        print(f"❌ Error testing Prometheus metrics: {e}")
        import traceback
        traceback.print_exc()

async def test_personalized_search_integration():
    """Test the integrated personalized search with quality scoring"""
    print("\n🧪 Testing Personalized Search Integration")
    print("=" * 50)
    
    try:
        import httpx
        
        # Test data
        test_request = {
            "user_id": "test-metrics-user",
            "force_refresh": True,
            "limit": 5
        }
        
        # Create test user preferences
        from supabase_client import supabase
        test_preferences = {
            "user_id": "test-metrics-user",
            "preferred_locations": ["San Francisco, CA"],
            "preferred_job_types": ["Full-time"],
            "preferred_industries": ["Technology"],
            "experience_level": "mid",
            "min_salary": 80000,
            "max_salary": 150000,
            "plan": "pro"
        }
        
        supabase.table("user_job_preferences").upsert(test_preferences).execute()
        print("✅ Test user preferences created")
        
        # Make API call
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:8000/api/jobs/personalized-search",
                json=test_request,
                timeout=30.0
            )
            
            if response.status_code == 200:
                data = response.json()
                jobs = data.get('jobs', [])
                
                print(f"✅ Personalized search successful: {len(jobs)} jobs returned")
                
                # Check for quality scores
                quality_scores = []
                for job in jobs[:3]:  # Check first 3 jobs
                    if 'qualityScore' in job:
                        quality_scores.append(job['qualityScore'])
                        print(f"   Job: {job.get('title', 'Unknown')} - Quality: {job['qualityScore']:.3f}")
                    else:
                        print(f"   ❌ Job missing quality score: {job.get('title', 'Unknown')}")
                
                if quality_scores:
                    avg_quality = sum(quality_scores) / len(quality_scores)
                    print(f"📊 Average quality score: {avg_quality:.3f}")
                    print("✅ Quality scoring integration working")
                else:
                    print("❌ No quality scores found in response")
                
            else:
                print(f"❌ Personalized search failed: {response.status_code}")
                print(f"Response: {response.text}")
        
        # Clean up
        supabase.table("user_job_preferences").delete().eq("user_id", "test-metrics-user").execute()
        print("✅ Test user preferences cleaned up")
        
    except Exception as e:
        print(f"❌ Error testing personalized search integration: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Run all tests"""
    print("🚀 Starting Metrics & Quality System Tests")
    print("=" * 60)
    
    # Test metrics endpoint
    await test_metrics_endpoint()
    
    # Test quality scoring
    await test_quality_scoring()
    
    # Test Prometheus metrics
    await test_prometheus_metrics()
    
    # Test integration
    await test_personalized_search_integration()
    
    print("\n" + "=" * 60)
    print("✅ All tests completed!")
    print("\nNext steps:")
    print("1. Start monitoring stack: docker-compose -f docker-compose.monitoring.yml up -d")
    print("2. Access Grafana: http://localhost:3000 (admin/admin123)")
    print("3. Access Prometheus: http://localhost:9090")
    print("4. Check metrics: curl http://localhost:8000/metrics")
    print("5. Test alerts by simulating API failures")

if __name__ == "__main__":
    asyncio.run(main())
