# jooble_service.py
import os
import asyncio
import httpx
import json
from typing import List, Dict, Any

# Jooble API configuration
JOOBLE_API_KEY = "9908d703-d6b5-4349-a712-6b5f01483150"
JOOBLE_HOST = "jooble.org"
JOOBLE_ENDPOINT = f"/api/{JOOBLE_API_KEY}"

async def jooble(client: httpx.AsyncClient, keywords: str = "developer", location: str = "Remote") -> List[Dict[str, Any]]:
    """Fetch jobs from Jooble API"""
    try:
        print(f"🔄 Fetching from Jooble API with keywords: {keywords}, location: {location}...")
        
        # Jooble API request body
        body = {
            "keywords": keywords,
            "location": location
        }
        
        headers = {"Content-type": "application/json"}
        
        url = f"https://{JOOBLE_HOST}{JOOBLE_ENDPOINT}"
        
        r = await client.post(url, 
                             json=body, 
                             headers=headers, 
                             timeout=20)
        r.raise_for_status()
        
        data = r.json()
        jobs = data.get("jobs", [])
        
        # Add source and external_id for each job
        for job in jobs:
            job["source"] = "jooble"
            job["external_id"] = str(job.get("id", ""))
            
        print(f"✅ Jooble: Found {len(jobs)} jobs")
        return jobs
    except Exception as e:
        print(f"❌ Jooble error: {e}")
        return []

def map_jooble_job(j: Dict[str, Any]) -> Dict[str, Any]:
    """Map Jooble job data to standardized format"""
    try:
        payload = {
            "title": j.get("title", ""),
            "company": j.get("company", ""),
            "location": j.get("location", "Remote"),
            "salary": j.get("salary", "Competitive"),
            "logo": j.get("logo", ""),
            "apply_url": j.get("link", ""),
            "description": j.get("snippet", "")[:1000],  # Jooble uses 'snippet' for description
            "source": "jooble",
            "external_id": str(j.get("id", "")),
        }
        return payload
    except Exception as e:
        print(f"Error mapping Jooble job: {e}")
        return {
            "title": j.get("title", "Unknown Job"),
            "company": j.get("company", "Unknown Company"),
            "description": "Jooble job data could not be properly parsed.",
            "source": "jooble",
            "external_id": str(j.get("id", "")),
        }
