import React from 'react';
import { JobFilters } from '@/services/jobFilterService';

interface QuickFilterBarProps {
  filters: JobFilters;
  onFilterPress: () => void;
  onQuickFilterToggle: (filterKey: keyof JobFilters['quickFilters']) => void;
  onLocationPress: () => void;
  onLocationSettingsPress?: () => void;
  userLocation?: string;
}

export default function QuickFilterBar({
  filters,
  onFilterPress,
  onQuickFilterToggle,
  onLocationPress,
  onLocationSettingsPress,
  userLocation
}: QuickFilterBarProps) {
  // Hide the old filter bar since we're using the new card-based system
  return null;
}
