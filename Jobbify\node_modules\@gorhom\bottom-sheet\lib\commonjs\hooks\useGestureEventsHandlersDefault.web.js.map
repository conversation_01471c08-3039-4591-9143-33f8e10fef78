{"version": 3, "names": ["_reactNative", "require", "_reactNativeReanimated", "_constants", "_clamp", "_snapPoint", "_useBottomSheetInternal", "INITIAL_CONTEXT", "initialPosition", "initialTranslationY", "initialKeyboardState", "KEYBOARD_STATE", "UNDETERMINED", "isScrollablePositionLocked", "dismissKeyboardOnJs", "runOnJS", "Keyboard", "dismiss", "resetContext", "context", "Object", "keys", "map", "key", "undefined", "useGestureEventsHandlersDefault", "animatedPosition", "animatedSnapPoints", "animatedKeyboardState", "animatedKeyboardHeight", "animatedContainerHeight", "animatedScrollableType", "animatedHighestSnapPoint", "animatedClosedPosition", "animatedScrollableContentOffsetY", "enableOverDrag", "enablePanDownToClose", "overDragResistanceFactor", "isInTemporaryPosition", "isScrollableRefreshable", "animateToPosition", "stopAnimation", "useBottomSheetInternal", "useSharedValue", "handleOnStart", "useWorkletCallback", "__", "translationY", "value", "handleOnChange", "source", "highestSnapPoint", "SHOWN", "lowestSnapPoint", "GESTURE_SOURCE", "CONTENT", "negativeScrollableContentOffset", "draggedPosition", "accumulatedDraggedPosition", "clampedPosition", "clamp", "HANDLE", "SCROLLABLE_TYPE", "VIEW", "resistedPosition", "Math", "sqrt", "handleOnEnd", "absoluteY", "velocityY", "isSheetAtHighestSnapPoint", "ANIMATION_SOURCE", "GESTURE", "isScrollable", "Platform", "OS", "WINDOW_HEIGHT", "snapPoints", "slice", "unshift", "destinationPoint", "snapPoint", "wasGestureHandledByScrollView", "handleOnFinalize", "exports"], "sourceRoot": "../../../src", "sources": ["hooks/useGestureEventsHandlersDefault.web.tsx"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAD,OAAA;AAKA,IAAAE,UAAA,GAAAF,OAAA;AAQA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAJ,OAAA;AACA,IAAAK,uBAAA,GAAAL,OAAA;AASA,MAAMM,eAAwC,GAAG;EAC/CC,eAAe,EAAE,CAAC;EAClBC,mBAAmB,EAAE,CAAC;EACtBC,oBAAoB,EAAEC,yBAAc,CAACC,YAAY;EACjDC,0BAA0B,EAAE;AAC9B,CAAC;AAED,MAAMC,mBAAmB,GAAG,IAAAC,8BAAO,EAACC,qBAAQ,CAACC,OAAO,CAAC;;AAErD;AACA,MAAMC,YAAY,GAAIC,OAAY,IAAK;EACrC,SAAS;;EACTC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,GAAG,CAACC,GAAG,IAAI;IAC9BJ,OAAO,CAACI,GAAG,CAAC,GAAGC,SAAS;EAC1B,CAAC,CAAC;AACJ,CAAC;AAEM,MAAMC,+BAA+B,GAAGA,CAAA,KAAM;EACnD;EACA,MAAM;IACJC,gBAAgB;IAChBC,kBAAkB;IAClBC,qBAAqB;IACrBC,sBAAsB;IACtBC,uBAAuB;IACvBC,sBAAsB;IACtBC,wBAAwB;IACxBC,sBAAsB;IACtBC,gCAAgC;IAChCC,cAAc;IACdC,oBAAoB;IACpBC,wBAAwB;IACxBC,qBAAqB;IACrBC,uBAAuB;IACvBC,iBAAiB;IACjBC;EACF,CAAC,GAAG,IAAAC,8CAAsB,EAAC,CAAC;EAE5B,MAAMvB,OAAO,GAAG,IAAAwB,qCAAc,EAA0B;IACtD,GAAGpC;EACL,CAAC,CAAC;EACF;;EAEA;EACA,MAAMqC,aAA8C,GAAG,IAAAC,yCAAkB,EACvE,SAASD,aAAaA,CAACE,EAAE,EAAE;IAAEC;EAAa,CAAC,EAAE;IAC3C;IACAN,aAAa,CAAC,CAAC;;IAEf;IACAtB,OAAO,CAAC6B,KAAK,GAAG;MACd,GAAG7B,OAAO,CAAC6B,KAAK;MAChBxC,eAAe,EAAEkB,gBAAgB,CAACsB,KAAK;MACvCtC,oBAAoB,EAAEkB,qBAAqB,CAACoB,KAAK;MACjDvC,mBAAmB,EAAEsC;IACvB,CAAC;;IAED;AACN;AACA;AACA;IACM,IAAIb,gCAAgC,CAACc,KAAK,GAAG,CAAC,EAAE;MAC9C7B,OAAO,CAAC6B,KAAK,CAACnC,0BAA0B,GAAG,IAAI;IACjD;EACF,CAAC,EACD,CACE4B,aAAa,EACbf,gBAAgB,EAChBE,qBAAqB,EACrBM,gCAAgC,CAEpC,CAAC;EACD,MAAMe,cAA+C,GAAG,IAAAJ,yCAAkB,EACxE,SAASI,cAAcA,CAACC,MAAM,EAAE;IAAEH;EAAa,CAAC,EAAE;IAChD,IAAII,gBAAgB,GAAGnB,wBAAwB,CAACgB,KAAK;IAErDD,YAAY,GAAGA,YAAY,GAAG5B,OAAO,CAAC6B,KAAK,CAACvC,mBAAmB;IAC/D;AACN;AACA;AACA;IACM,IACE6B,qBAAqB,CAACU,KAAK,IAC3B7B,OAAO,CAAC6B,KAAK,CAACtC,oBAAoB,KAAKC,yBAAc,CAACyC,KAAK,EAC3D;MACAD,gBAAgB,GAAGhC,OAAO,CAAC6B,KAAK,CAACxC,eAAe;IAClD;;IAEA;AACN;AACA;AACA;IACM,IACE8B,qBAAqB,CAACU,KAAK,IAC3B7B,OAAO,CAAC6B,KAAK,CAACxC,eAAe,GAAG2C,gBAAgB,EAChD;MACAA,gBAAgB,GAAGhC,OAAO,CAAC6B,KAAK,CAACxC,eAAe;IAClD;IAEA,MAAM6C,eAAe,GAAGjB,oBAAoB,GACxCN,uBAAuB,CAACkB,KAAK,GAC7BrB,kBAAkB,CAACqB,KAAK,CAAC,CAAC,CAAC;;IAE/B;AACN;AACA;AACA;IACM,IACEE,MAAM,KAAKI,yBAAc,CAACC,OAAO,IACjChB,uBAAuB,CAACS,KAAK,IAC7BtB,gBAAgB,CAACsB,KAAK,KAAKG,gBAAgB,EAC3C;MACA;IACF;;IAEA;AACN;AACA;AACA;AACA;AACA;IACM,MAAMK,+BAA+B,GAClCrC,OAAO,CAAC6B,KAAK,CAACxC,eAAe,KAAK2C,gBAAgB,IACjDD,MAAM,KAAKI,yBAAc,CAACC,OAAO,IACnC,CAACpC,OAAO,CAAC6B,KAAK,CAACnC,0BAA0B,GACrCqB,gCAAgC,CAACc,KAAK,GAAG,CAAC,CAAC,GAC3C,CAAC;;IAEP;AACN;AACA;IACM,MAAMS,eAAe,GAAGtC,OAAO,CAAC6B,KAAK,CAACxC,eAAe,GAAGuC,YAAY;;IAEpE;AACN;AACA;AACA;AACA;IACM,MAAMW,0BAA0B,GAC9BD,eAAe,GAAGD,+BAA+B;;IAEnD;AACN;AACA;AACA;IACM,MAAMG,eAAe,GAAG,IAAAC,YAAK,EAC3BF,0BAA0B,EAC1BP,gBAAgB,EAChBE,eACF,CAAC;;IAED;AACN;AACA;AACA;IACM,IACElC,OAAO,CAAC6B,KAAK,CAACnC,0BAA0B,IACxCqC,MAAM,KAAKI,yBAAc,CAACC,OAAO,IACjC7B,gBAAgB,CAACsB,KAAK,KAAKG,gBAAgB,EAC3C;MACAhC,OAAO,CAAC6B,KAAK,CAACnC,0BAA0B,GAAG,KAAK;IAClD;;IAEA;AACN;AACA;IACM,IAAIsB,cAAc,EAAE;MAClB,IACE,CAACe,MAAM,KAAKI,yBAAc,CAACO,MAAM,IAC/B9B,sBAAsB,CAACiB,KAAK,KAAKc,0BAAe,CAACC,IAAI,KACvDN,eAAe,GAAGN,gBAAgB,EAClC;QACA,MAAMa,gBAAgB,GACpBb,gBAAgB,GAChBc,IAAI,CAACC,IAAI,CAAC,CAAC,IAAIf,gBAAgB,GAAGM,eAAe,CAAC,CAAC,GACjDpB,wBAAwB;QAC5BX,gBAAgB,CAACsB,KAAK,GAAGgB,gBAAgB;QACzC;MACF;MAEA,IACEd,MAAM,KAAKI,yBAAc,CAACO,MAAM,IAChCJ,eAAe,GAAGJ,eAAe,EACjC;QACA,MAAMW,gBAAgB,GACpBX,eAAe,GACfY,IAAI,CAACC,IAAI,CAAC,CAAC,IAAIT,eAAe,GAAGJ,eAAe,CAAC,CAAC,GAChDhB,wBAAwB;QAC5BX,gBAAgB,CAACsB,KAAK,GAAGgB,gBAAgB;QACzC;MACF;MAEA,IACEd,MAAM,KAAKI,yBAAc,CAACC,OAAO,IACjCE,eAAe,GAAGD,+BAA+B,GAAGH,eAAe,EACnE;QACA,MAAMW,gBAAgB,GACpBX,eAAe,GACfY,IAAI,CAACC,IAAI,CACP,CAAC,IACET,eAAe,GACdD,+BAA+B,GAC/BH,eAAe,CACrB,CAAC,GACChB,wBAAwB;QAC5BX,gBAAgB,CAACsB,KAAK,GAAGgB,gBAAgB;QACzC;MACF;IACF;IAEAtC,gBAAgB,CAACsB,KAAK,GAAGW,eAAe;EAC1C,CAAC,EACD,CACExB,cAAc,EACdC,oBAAoB,EACpBC,wBAAwB,EACxBC,qBAAqB,EACrBC,uBAAuB,EACvBP,wBAAwB,EACxBF,uBAAuB,EACvBH,kBAAkB,EAClBD,gBAAgB,EAChBK,sBAAsB,EACtBG,gCAAgC,CAEpC,CAAC;EACD,MAAMiC,WAA4C,GAAG,IAAAtB,yCAAkB,EACrE,SAASsB,WAAWA,CAACjB,MAAM,EAAE;IAAEH,YAAY;IAAEqB,SAAS;IAAEC;EAAU,CAAC,EAAE;IACnE,MAAMlB,gBAAgB,GAAGnB,wBAAwB,CAACgB,KAAK;IACvD,MAAMsB,yBAAyB,GAC7B5C,gBAAgB,CAACsB,KAAK,KAAKG,gBAAgB;;IAE7C;AACN;AACA;AACA;IACM,IACED,MAAM,KAAKI,yBAAc,CAACC,OAAO,IACjChB,uBAAuB,CAACS,KAAK,IAC7BsB,yBAAyB,EACzB;MACA;IACF;;IAEA;AACN;AACA;AACA;IACM,IACEhC,qBAAqB,CAACU,KAAK,IAC3B7B,OAAO,CAAC6B,KAAK,CAACxC,eAAe,IAAIkB,gBAAgB,CAACsB,KAAK,EACvD;MACA,IAAI7B,OAAO,CAAC6B,KAAK,CAACxC,eAAe,GAAGkB,gBAAgB,CAACsB,KAAK,EAAE;QAC1DR,iBAAiB,CACfrB,OAAO,CAAC6B,KAAK,CAACxC,eAAe,EAC7B+D,2BAAgB,CAACC,OAAO,EACxBH,SAAS,GAAG,CACd,CAAC;MACH;MACA;IACF;;IAEA;AACN;AACA;AACA;IACM,MAAMI,YAAY,GAChB1C,sBAAsB,CAACiB,KAAK,KAAKc,0BAAe,CAAClD,YAAY,IAC7DmB,sBAAsB,CAACiB,KAAK,KAAKc,0BAAe,CAACC,IAAI;;IAEvD;AACN;AACA;AACA;IACM,IACE5C,OAAO,CAAC6B,KAAK,CAACtC,oBAAoB,KAAKC,yBAAc,CAACyC,KAAK,IAC3D1B,gBAAgB,CAACsB,KAAK,GAAG7B,OAAO,CAAC6B,KAAK,CAACxC,eAAe,EACtD;MACA;AACR;AACA;AACA;AACA;AACA;AACA;MACQ,IACE,EACEkE,qBAAQ,CAACC,EAAE,KAAK,KAAK,IACrBF,YAAY,IACZL,SAAS,GAAGQ,wBAAa,GAAG/C,sBAAsB,CAACmB,KAAK,CACzD,EACD;QACAlC,mBAAmB,CAAC,CAAC;MACvB;IACF;;IAEA;AACN;AACA;IACM,IAAIwB,qBAAqB,CAACU,KAAK,EAAE;MAC/BV,qBAAqB,CAACU,KAAK,GAAG,KAAK;IACrC;;IAEA;AACN;AACA;AACA;IACM,MAAM6B,UAAU,GAAGlD,kBAAkB,CAACqB,KAAK,CAAC8B,KAAK,CAAC,CAAC;IACnD,IAAI1C,oBAAoB,EAAE;MACxByC,UAAU,CAACE,OAAO,CAAC9C,sBAAsB,CAACe,KAAK,CAAC;IAClD;;IAEA;AACN;AACA;IACM,MAAMgC,gBAAgB,GAAG,IAAAC,oBAAS,EAChClC,YAAY,GAAG5B,OAAO,CAAC6B,KAAK,CAACxC,eAAe,EAC5C6D,SAAS,EACTQ,UACF,CAAC;;IAED;AACN;AACA;AACA;IACM,IAAIG,gBAAgB,KAAKtD,gBAAgB,CAACsB,KAAK,EAAE;MAC/C;IACF;IAEA,MAAMkC,6BAA6B,GACjChC,MAAM,KAAKI,yBAAc,CAACC,OAAO,IACjCrB,gCAAgC,CAACc,KAAK,GAAG,CAAC;IAC5C;AACN;AACA;IACM,IAAIkC,6BAA6B,IAAIZ,yBAAyB,EAAE;MAC9D;IACF;IAEA9B,iBAAiB,CACfwC,gBAAgB,EAChBT,2BAAgB,CAACC,OAAO,EACxBH,SAAS,GAAG,CACd,CAAC;EACH,CAAC,EACD,CACEjC,oBAAoB,EACpBE,qBAAqB,EACrBC,uBAAuB,EACvBN,sBAAsB,EACtBD,wBAAwB,EACxBH,sBAAsB,EACtBH,gBAAgB,EAChBK,sBAAsB,EACtBJ,kBAAkB,EAClBO,gCAAgC,EAChCM,iBAAiB,CAErB,CAAC;EACD,MAAM2C,gBAAiD,GAAG,IAAAtC,yCAAkB,EAC1E,SAASsC,gBAAgBA,CAAA,EAAG;IAC1BjE,YAAY,CAACC,OAAO,CAAC;EACvB,CAAC,EACD,CAACA,OAAO,CACV,CAAC;EACD;;EAEA,OAAO;IACLyB,aAAa;IACbK,cAAc;IACdkB,WAAW;IACXgB;EACF,CAAC;AACH,CAAC;AAACC,OAAA,CAAA3D,+BAAA,GAAAA,+BAAA", "ignoreList": []}