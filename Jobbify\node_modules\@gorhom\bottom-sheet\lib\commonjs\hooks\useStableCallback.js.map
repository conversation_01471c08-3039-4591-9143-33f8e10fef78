{"version": 3, "names": ["_react", "require", "useStableCallback", "callback", "callback<PERSON><PERSON>", "useRef", "useLayoutEffect", "current", "useEffect", "undefined", "useCallback", "args"], "sourceRoot": "../../../src", "sources": ["hooks/useStableCallback.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAIA;AACA;AACA;AACO,SAASC,iBAAiBA,CAC/BC,QAAwB,EACxB;EACA,MAAMC,WAAW,GAAG,IAAAC,aAAM,EAAiB,CAAC;EAE5C,IAAAC,sBAAe,EAAC,MAAM;IACpBF,WAAW,CAACG,OAAO,GAAGJ,QAAQ;EAChC,CAAC,CAAC;EAEF,IAAAK,gBAAS,EAAC,MAAM;IACd,OAAO,MAAM;MACXJ,WAAW,CAACG,OAAO,GAAGE,SAAS;IACjC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO,IAAAC,kBAAW,EAA6B,CAAC,GAAGC,IAAI,KAAK;IAC1D,OAAOP,WAAW,CAACG,OAAO,GAAG,GAAGI,IAAI,CAAC;EACvC,CAAC,EAAE,EAAE,CAAC;AACR", "ignoreList": []}