import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';

interface ExperienceLevelCardProps {
  preferences: any;
  onNext: (data: any) => void;
  onPrevious?: () => void;
  theme: string;
  themeColors: any;
}

const EXPERIENCE_LEVELS = [
  {
    id: 'entry',
    title: 'Entry Level',
    subtitle: '0-2 years of experience',
    icon: 'play-circle',
    color: '#10B981',
    yearsRange: '0-2 years'
  },
  {
    id: 'junior',
    title: 'Junior',
    subtitle: '1-3 years of experience',
    icon: 'arrow-up',
    color: '#3B82F6',
    yearsRange: '1-3 years'
  },
  {
    id: 'mid',
    title: 'Mid Level',
    subtitle: '3-5 years of experience',
    icon: 'star-half-o',
    color: '#F59E0B',
    yearsRange: '3-5 years'
  },
  {
    id: 'senior',
    title: 'Senior',
    subtitle: '5-8 years of experience',
    icon: 'star',
    color: '#8B5CF6',
    yearsRange: '5-8 years'
  },
  {
    id: 'lead',
    title: 'Lead',
    subtitle: '7+ years with leadership',
    icon: 'users',
    color: '#EF4444',
    yearsRange: '7+ years'
  },
  {
    id: 'executive',
    title: 'Executive',
    subtitle: '10+ years, C-level',
    icon: 'trophy',
    color: '#F97316',
    yearsRange: '10+ years'
  }
];

export default function ExperienceLevelCard({ 
  preferences, 
  onNext, 
  onPrevious, 
  theme, 
  themeColors 
}: ExperienceLevelCardProps) {
  const [selectedLevel, setSelectedLevel] = useState(preferences.experienceLevel || '');

  const handleNext = () => {
    if (!selectedLevel) {
      Alert.alert('Experience Level Required', 'Please select your experience level.');
      return;
    }
    onNext({ experienceLevel: selectedLevel });
  };

  const renderExperienceOption = (level: typeof EXPERIENCE_LEVELS[0]) => {
    const isSelected = selectedLevel === level.id;
    
    return (
      <TouchableOpacity
        key={level.id}
        style={[
          styles.levelCard,
          {
            backgroundColor: isSelected ? level.color : themeColors.card,
            borderColor: isSelected ? level.color : themeColors.border,
          }
        ]}
        onPress={() => setSelectedLevel(level.id)}
      >
        <View style={[
          styles.iconContainer,
          { backgroundColor: isSelected ? 'rgba(255,255,255,0.2)' : level.color + '20' }
        ]}>
          <FontAwesome 
            name={level.icon as any} 
            size={22} 
            color={isSelected ? '#FFFFFF' : level.color} 
          />
        </View>
        
        <View style={styles.textContainer}>
          <Text style={[
            styles.levelTitle,
            { color: isSelected ? '#FFFFFF' : themeColors.text }
          ]}>
            {level.title}
          </Text>
          <Text style={[
            styles.levelSubtitle,
            { color: isSelected ? 'rgba(255,255,255,0.8)' : themeColors.textSecondary }
          ]}>
            {level.subtitle}
          </Text>
          <Text style={[
            styles.yearsRange,
            { color: isSelected ? 'rgba(255,255,255,0.9)' : level.color }
          ]}>
            {level.yearsRange}
          </Text>
        </View>

        {isSelected && (
          <View style={styles.checkContainer}>
            <FontAwesome name="check-circle" size={20} color="#FFFFFF" />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <Animated.View entering={FadeInUp.delay(200)} style={styles.header}>
        <Text style={[styles.title, { color: themeColors.text }]}>
          What's your experience level?
        </Text>
        <Text style={[styles.subtitle, { color: themeColors.textSecondary }]}>
          This helps us match you with appropriate roles
        </Text>
      </Animated.View>

      <Animated.View entering={FadeInUp.delay(400)} style={styles.content}>
        {EXPERIENCE_LEVELS.map(renderExperienceOption)}
      </Animated.View>

      <Animated.View entering={FadeInDown.delay(600)} style={styles.buttonContainer}>
        <View style={styles.buttonRow}>
          {onPrevious && (
            <TouchableOpacity
              style={[styles.backButton, { borderColor: themeColors.border }]}
              onPress={onPrevious}
            >
              <FontAwesome name="arrow-left" size={16} color={themeColors.textSecondary} />
              <Text style={[styles.backText, { color: themeColors.textSecondary }]}>
                Back
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[
              styles.nextButton,
              { 
                backgroundColor: selectedLevel ? themeColors.tint : themeColors.border,
                opacity: selectedLevel ? 1 : 0.5
              },
              !onPrevious && styles.fullWidthButton
            ]}
            onPress={handleNext}
            disabled={!selectedLevel}
          >
            <Text style={styles.nextText}>Continue</Text>
            <FontAwesome name="arrow-right" size={16} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
    paddingTop: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    gap: 12,
  },
  levelCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 2,
    minHeight: 70,
  },
  iconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  levelTitle: {
    fontSize: 17,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  levelSubtitle: {
    fontSize: 14,
    lineHeight: 18,
    marginBottom: 2,
  },
  yearsRange: {
    fontSize: 12,
    fontWeight: '600',
  },
  checkContainer: {
    marginLeft: 12,
  },
  buttonContainer: {
    paddingBottom: 32,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 2,
    gap: 8,
    flex: 1,
  },
  backText: {
    fontSize: 16,
    fontWeight: '600',
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
    flex: 2,
  },
  fullWidthButton: {
    flex: 1,
  },
  nextText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
