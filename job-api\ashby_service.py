# ashby_service.py
import os
import asyncio
import httpx
from typing import List, Dict, Any

# Ashby API configuration
# You can set this in environment or replace with your actual job board name
ASHBY_JOB_BOARD_NAME = os.getenv("ASHBY_JOB_BOARD_NAME", "ashby")  # Using "ashby" as default since it works
ASHBY_BASE_URL = "https://api.ashbyhq.com/posting-api"

async def ashby(client: httpx.AsyncClient, job_board_name: str = None) -> List[Dict[str, Any]]:
    """Fetch jobs from Ashby API"""
    try:
        board_name = job_board_name or ASHBY_JOB_BOARD_NAME
        print(f"🔄 Fetching from Ashby API for job board: {board_name}...")
        
        headers = {
            "User-Agent": "JobbifyBot/1.0 (+https://jobbify.app)",
            "Accept": "application/json"
        }
        
        params = {
            "includeCompensation": "true"
        }
        
        url = f"{ASHBY_BASE_URL}/job-board/{board_name}"
        
        r = await client.get(url, 
                            params=params,
                            headers=headers, 
                            timeout=20)
        r.raise_for_status()
        
        data = r.json()
        jobs = data.get("jobs", [])
        
        # Add source and external_id for each job
        for job in jobs:
            job["source"] = "ashby"
            # Use the jobUrl as a unique identifier since Ashby doesn't provide explicit IDs
            job["external_id"] = job.get("jobUrl", "").split("/")[-1] if job.get("jobUrl") else ""
            
        print(f"✅ Ashby: Found {len(jobs)} jobs")
        return jobs
    except Exception as e:
        print(f"❌ Ashby error: {e}")
        return []

def map_ashby_job(j: Dict[str, Any]) -> Dict[str, Any]:
    """Map Ashby job data to standardized format"""
    try:
        # Extract location information
        location = j.get("location", "Remote")
        if j.get("isRemote"):
            location = "Remote"
        elif j.get("address", {}).get("postalAddress"):
            postal = j["address"]["postalAddress"]
            city = postal.get("addressLocality", "")
            region = postal.get("addressRegion", "")
            if city and region:
                location = f"{city}, {region}"
            elif city:
                location = city
        
        # Extract salary from compensation data
        salary = "Competitive"
        compensation = j.get("compensation", {})
        if compensation:
            salary_summary = compensation.get("scrapeableCompensationSalarySummary")
            if salary_summary:
                salary = salary_summary
            else:
                tier_summary = compensation.get("compensationTierSummary")
                if tier_summary:
                    salary = tier_summary
        
        # Extract company logo (Ashby doesn't provide this directly, so we'll generate one)
        company_name = j.get("department", "Company")
        logo = f"https://ui-avatars.com/api/?name={company_name}&background=random&size=150"
        
        payload = {
            "title": j.get("title", ""),
            "company": company_name,  # Using department as company for now
            "location": location,
            "salary": salary,
            "logo": logo,
            "apply_url": j.get("applyUrl", ""),
            "description": j.get("descriptionPlain", "")[:1000],  # Use plain text description
            "source": "ashby",
            "external_id": j.get("jobUrl", "").split("/")[-1] if j.get("jobUrl") else "",
        }
        
        return payload
    except Exception as e:
        print(f"Error mapping Ashby job: {e}")
        return {
            "title": j.get("title", "Unknown Job"),
            "company": j.get("department", "Unknown Company"),
            "description": "Ashby job data could not be properly parsed.",
            "source": "ashby",
            "external_id": j.get("jobUrl", "").split("/")[-1] if j.get("jobUrl") else "",
        }
