{"version": 3, "names": ["_react", "require", "_hooks", "useBottomSheetContentSizeSetter", "enableDynamicSizing", "animatedContentHeight", "useBottomSheetInternal", "setContentSize", "useCallback", "contentHeight", "set"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetScrollable/useBottomSheetContentSizeSetter.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAEA;AACA;AACA;AACA;AACO,SAASE,+BAA+BA,CAAA,EAAG;EAChD;EACA,MAAM;IAAEC,mBAAmB;IAAEC;EAAsB,CAAC,GAClD,IAAAC,6BAAsB,EAAC,CAAC;EAC1B;;EAEA;EACA,MAAMC,cAAc,GAAG,IAAAC,kBAAW,EAC/BC,aAAqB,IAAK;IACzB,IAAI,CAACL,mBAAmB,EAAE;MACxB;IACF;IACAC,qBAAqB,CAACK,GAAG,CAACD,aAAa,CAAC;EAC1C,CAAC,EACD,CAACL,mBAAmB,EAAEC,qBAAqB,CAC7C,CAAC;EACD;;EAEA,OAAO;IACLE;EACF,CAAC;AACH", "ignoreList": []}