import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';

interface JobCategoriesCardProps {
  preferences: any;
  onNext: (data: any) => void;
  onPrevious?: () => void;
  theme: string;
  themeColors: any;
}

const JOB_CATEGORIES = [
  {
    id: 'software-engineer',
    title: 'Software Engineer',
    icon: 'code',
    color: '#3B82F6'
  },
  {
    id: 'data-scientist',
    title: 'Data Scientist',
    icon: 'bar-chart',
    color: '#10B981'
  },
  {
    id: 'product-manager',
    title: 'Product Manager',
    icon: 'lightbulb-o',
    color: '#F59E0B'
  },
  {
    id: 'designer',
    title: 'Designer',
    icon: 'paint-brush',
    color: '#EF4444'
  },
  {
    id: 'marketing',
    title: 'Marketing',
    icon: 'bullhorn',
    color: '#8B5CF6'
  },
  {
    id: 'sales',
    title: 'Sales',
    icon: 'handshake-o',
    color: '#F97316'
  },
  {
    id: 'customer-success',
    title: 'Customer Success',
    icon: 'heart',
    color: '#EC4899'
  },
  {
    id: 'finance',
    title: 'Finance',
    icon: 'calculator',
    color: '#06B6D4'
  },
  {
    id: 'operations',
    title: 'Operations',
    icon: 'cogs',
    color: '#84CC16'
  },
  {
    id: 'hr',
    title: 'Human Resources',
    icon: 'users',
    color: '#A855F7'
  },
  {
    id: 'legal',
    title: 'Legal',
    icon: 'gavel',
    color: '#64748B'
  },
  {
    id: 'consulting',
    title: 'Consulting',
    icon: 'briefcase',
    color: '#0F172A'
  },
  {
    id: 'healthcare',
    title: 'Healthcare',
    icon: 'stethoscope',
    color: '#DC2626'
  },
  {
    id: 'education',
    title: 'Education',
    icon: 'graduation-cap',
    color: '#059669'
  },
  {
    id: 'media',
    title: 'Media & Creative',
    icon: 'camera',
    color: '#7C3AED'
  },
  {
    id: 'other',
    title: 'Other',
    icon: 'ellipsis-h',
    color: '#6B7280'
  }
];

export default function JobCategoriesCard({ 
  preferences, 
  onNext, 
  onPrevious, 
  theme, 
  themeColors 
}: JobCategoriesCardProps) {
  const [selectedCategories, setSelectedCategories] = useState<string[]>(preferences.jobCategories || []);

  const toggleCategory = (categoryId: string) => {
    setSelectedCategories(prev => {
      if (prev.includes(categoryId)) {
        return prev.filter(id => id !== categoryId);
      } else {
        return [...prev, categoryId];
      }
    });
  };

  const handleNext = () => {
    if (selectedCategories.length === 0) {
      Alert.alert('Job Categories Required', 'Please select at least one job category.');
      return;
    }
    onNext({ jobCategories: selectedCategories });
  };

  const renderCategoryOption = (category: typeof JOB_CATEGORIES[0]) => {
    const isSelected = selectedCategories.includes(category.id);
    
    return (
      <TouchableOpacity
        key={category.id}
        style={[
          styles.categoryCard,
          {
            backgroundColor: isSelected ? category.color : themeColors.card,
            borderColor: isSelected ? category.color : themeColors.border,
          }
        ]}
        onPress={() => toggleCategory(category.id)}
      >
        <View style={[
          styles.iconContainer,
          { backgroundColor: isSelected ? 'rgba(255,255,255,0.2)' : category.color + '20' }
        ]}>
          <FontAwesome 
            name={category.icon as any} 
            size={18} 
            color={isSelected ? '#FFFFFF' : category.color} 
          />
        </View>
        
        <Text style={[
          styles.categoryTitle,
          { color: isSelected ? '#FFFFFF' : themeColors.text }
        ]}>
          {category.title}
        </Text>

        <View style={[
          styles.checkbox,
          {
            backgroundColor: isSelected ? '#FFFFFF' : 'transparent',
            borderColor: isSelected ? '#FFFFFF' : themeColors.border,
          }
        ]}>
          {isSelected && (
            <FontAwesome name="check" size={10} color={category.color} />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <Animated.View entering={FadeInUp.delay(200)} style={styles.header}>
        <Text style={[styles.title, { color: themeColors.text }]}>
          What type of roles interest you?
        </Text>
        <Text style={[styles.subtitle, { color: themeColors.textSecondary }]}>
          Select all categories that apply
        </Text>
      </Animated.View>

      <Animated.View entering={FadeInUp.delay(400)} style={styles.content}>
        <ScrollView 
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          <View style={styles.categoriesGrid}>
            {JOB_CATEGORIES.map(renderCategoryOption)}
          </View>
        </ScrollView>
        
        {selectedCategories.length > 0 && (
          <Animated.View entering={FadeInUp.delay(200)} style={styles.selectedContainer}>
            <Text style={[styles.selectedText, { color: themeColors.textSecondary }]}>
              {selectedCategories.length} categor{selectedCategories.length > 1 ? 'ies' : 'y'} selected
            </Text>
          </Animated.View>
        )}
      </Animated.View>

      <Animated.View entering={FadeInDown.delay(600)} style={styles.buttonContainer}>
        <View style={styles.buttonRow}>
          {onPrevious && (
            <TouchableOpacity
              style={[styles.backButton, { borderColor: themeColors.border }]}
              onPress={onPrevious}
            >
              <FontAwesome name="arrow-left" size={16} color={themeColors.textSecondary} />
              <Text style={[styles.backText, { color: themeColors.textSecondary }]}>
                Back
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[
              styles.nextButton,
              { 
                backgroundColor: selectedCategories.length > 0 ? themeColors.tint : themeColors.border,
                opacity: selectedCategories.length > 0 ? 1 : 0.5
              },
              !onPrevious && styles.fullWidthButton
            ]}
            onPress={handleNext}
            disabled={selectedCategories.length === 0}
          >
            <Text style={styles.nextText}>Continue</Text>
            <FontAwesome name="arrow-right" size={16} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
    paddingTop: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 16,
  },
  categoriesGrid: {
    gap: 10,
  },
  categoryCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 2,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryTitle: {
    fontSize: 15,
    fontWeight: '600',
    flex: 1,
  },
  checkbox: {
    width: 18,
    height: 18,
    borderRadius: 3,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  selectedContainer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  selectedText: {
    fontSize: 14,
    fontWeight: '500',
  },
  buttonContainer: {
    paddingBottom: 32,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 2,
    gap: 8,
    flex: 1,
  },
  backText: {
    fontSize: 16,
    fontWeight: '600',
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
    flex: 2,
  },
  fullWidthButton: {
    flex: 1,
  },
  nextText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
