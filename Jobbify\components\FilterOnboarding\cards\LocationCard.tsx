import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  FlatList,
  Alert
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
// Removed complex animations that might cause crashes

interface LocationCardProps {
  preferences: any;
  onNext: (data: any) => void;
  onPrevious?: () => void;
  theme: string;
  themeColors: any;
}

const POPULAR_LOCATIONS = [
  'San Francisco, CA',
  'New York, NY',
  'Los Angeles, CA',
  'Seattle, WA',
  'Austin, TX',
  'Boston, MA',
  'Chicago, IL',
  'Denver, CO',
  'Remote',
  'Anywhere in the US'
];

export default function LocationCard({ 
  preferences, 
  onNext, 
  onPrevious, 
  theme, 
  themeColors 
}: LocationCardProps) {
  const [selectedLocation, setSelectedLocation] = useState(preferences.location || '');
  const [customLocation, setCustomLocation] = useState('');
  const [showCustomInput, setShowCustomInput] = useState(false);

  const handleLocationSelect = (location: string) => {
    setSelectedLocation(location);
    setShowCustomInput(false);
    setCustomLocation('');
  };

  const handleCustomLocation = () => {
    setShowCustomInput(true);
    setSelectedLocation('');
  };

  const handleNext = () => {
    try {
      const location = showCustomInput ? customLocation : selectedLocation;
      if (!location.trim()) {
        Alert.alert('Location Required', 'Please select or enter your preferred location.');
        return;
      }
      console.log('[LOCATION_CARD] Proceeding with location:', location.trim());
      onNext({ location: location.trim() });
    } catch (error) {
      console.error('[LOCATION_CARD] Error in handleNext:', error);
      // Fallback to Remote if there's an error
      onNext({ location: 'Remote' });
    }
  };

  const renderLocationItem = ({ item }: { item: string }) => (
    <TouchableOpacity
      style={[
        styles.locationItem,
        {
          backgroundColor: selectedLocation === item ? themeColors.tint : themeColors.card,
          borderColor: selectedLocation === item ? themeColors.tint : themeColors.border,
        }
      ]}
      onPress={() => handleLocationSelect(item)}
    >
      <FontAwesome 
        name={item === 'Remote' ? 'home' : 'map-marker'} 
        size={16} 
        color={selectedLocation === item ? '#FFFFFF' : themeColors.textSecondary} 
      />
      <Text style={[
        styles.locationText,
        { color: selectedLocation === item ? '#FFFFFF' : themeColors.text }
      ]}>
        {item}
      </Text>
      {selectedLocation === item && (
        <FontAwesome name="check" size={16} color="#FFFFFF" />
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: themeColors.text }]}>
          Where do you want to work?
        </Text>
        <Text style={[styles.subtitle, { color: themeColors.textSecondary }]}>
          Select your preferred location to find jobs near you
        </Text>
      </View>

      <View style={styles.content}>
        <FlatList
          data={POPULAR_LOCATIONS}
          renderItem={renderLocationItem}
          keyExtractor={(item) => item}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.locationsList}
        />

        <TouchableOpacity
          style={[
            styles.customLocationButton,
            {
              backgroundColor: showCustomInput ? themeColors.tint : themeColors.card,
              borderColor: showCustomInput ? themeColors.tint : themeColors.border,
            }
          ]}
          onPress={handleCustomLocation}
        >
          <FontAwesome 
            name="plus" 
            size={16} 
            color={showCustomInput ? '#FFFFFF' : themeColors.textSecondary} 
          />
          <Text style={[
            styles.customLocationText,
            { color: showCustomInput ? '#FFFFFF' : themeColors.text }
          ]}>
            Enter custom location
          </Text>
        </TouchableOpacity>

        {showCustomInput && (
          <View style={styles.customInputContainer}>
            <TextInput
              style={[
                styles.customInput,
                {
                  backgroundColor: themeColors.card,
                  borderColor: themeColors.border,
                  color: themeColors.text,
                }
              ]}
              placeholder="Enter city, state or country"
              placeholderTextColor={themeColors.textSecondary}
              value={customLocation}
              onChangeText={setCustomLocation}
              autoFocus
            />
          </View>
        )}
      </View>

      <View style={styles.buttonContainer}>
        <View style={styles.buttonRow}>
          {onPrevious && (
            <TouchableOpacity
              style={[styles.backButton, { borderColor: themeColors.border }]}
              onPress={onPrevious}
            >
              <FontAwesome name="arrow-left" size={16} color={themeColors.textSecondary} />
              <Text style={[styles.backText, { color: themeColors.textSecondary }]}>
                Back
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[
              styles.nextButton,
              { backgroundColor: themeColors.tint },
              !onPrevious && styles.fullWidthButton
            ]}
            onPress={handleNext}
          >
            <Text style={styles.nextText}>Continue</Text>
            <FontAwesome name="arrow-right" size={16} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
    paddingTop: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  content: {
    flex: 1,
  },
  locationsList: {
    paddingBottom: 16,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 2,
    marginBottom: 12,
    gap: 12,
  },
  locationText: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  customLocationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 2,
    marginBottom: 16,
    gap: 12,
  },
  customLocationText: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  customInputContainer: {
    marginBottom: 16,
  },
  customInput: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 2,
    fontSize: 16,
  },
  buttonContainer: {
    paddingBottom: 32,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 2,
    gap: 8,
    flex: 1,
  },
  backText: {
    fontSize: 16,
    fontWeight: '600',
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
    flex: 2,
  },
  fullWidthButton: {
    flex: 1,
  },
  nextText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
