"""
Job Quality Scoring System
Assigns quality scores to jobs based on multiple factors
"""

import re
from datetime import datetime, timedelta
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)

def calculate_days_old(job: Dict[str, Any]) -> int:
    """Calculate how many days old a job posting is"""
    try:
        # Try different date fields that might exist
        date_fields = ['datePosted', 'posted_date', 'created_at', 'published_date', 'date']
        
        for field in date_fields:
            if field in job and job[field]:
                date_str = job[field]
                
                # Handle different date formats
                try:
                    if isinstance(date_str, str):
                        # Try ISO format first
                        if 'T' in date_str:
                            job_date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                        else:
                            # Try common date formats
                            for fmt in ['%Y-%m-%d', '%m/%d/%Y', '%d/%m/%Y']:
                                try:
                                    job_date = datetime.strptime(date_str, fmt)
                                    break
                                except ValueError:
                                    continue
                            else:
                                continue
                    else:
                        continue
                    
                    # Calculate days difference
                    days_old = (datetime.now() - job_date.replace(tzinfo=None)).days
                    return max(0, days_old)
                    
                except (ValueError, TypeError):
                    continue
        
        # If no valid date found, assume it's recent (3 days old)
        return 3
        
    except Exception as e:
        logger.warning(f"Error calculating job age: {e}")
        return 7  # Default to 1 week old

def extract_salary_amount(salary_text: str) -> float:
    """Extract numeric salary amount from salary text"""
    if not salary_text:
        return 0
    
    try:
        # Remove common currency symbols and text
        cleaned = re.sub(r'[^\d.,k]', '', str(salary_text).lower())
        
        # Handle 'k' notation (e.g., "50k" -> 50000)
        if 'k' in cleaned:
            cleaned = cleaned.replace('k', '')
            multiplier = 1000
        else:
            multiplier = 1
        
        # Extract first number found
        numbers = re.findall(r'\d+(?:\.\d+)?', cleaned)
        if numbers:
            return float(numbers[0]) * multiplier
        
        return 0
    except:
        return 0

def has_company_logo(job: Dict[str, Any]) -> bool:
    """Check if job has a company logo"""
    logo_fields = ['company_logo', 'companyLogo', 'logo', 'company_logo_url', 'logoUrl']
    
    for field in logo_fields:
        if field in job and job[field]:
            logo_value = job[field]
            if isinstance(logo_value, str) and len(logo_value.strip()) > 0:
                # Check if it's not a placeholder or default logo
                if not any(placeholder in logo_value.lower() for placeholder in 
                          ['placeholder', 'default', 'no-logo', 'missing']):
                    return True
    
    return False

def calculate_description_quality(description: str) -> float:
    """Calculate description quality based on length and content"""
    if not description:
        return 0
    
    # Base score on length (longer descriptions are generally better)
    length_score = min(len(description) / 2000, 1.0)  # Cap at 2000 chars
    
    # Bonus for structured content
    structure_bonus = 0
    if any(keyword in description.lower() for keyword in 
           ['responsibilities', 'requirements', 'qualifications', 'benefits']):
        structure_bonus = 0.1
    
    # Penalty for very short descriptions
    if len(description) < 100:
        length_score *= 0.5
    
    return min(length_score + structure_bonus, 1.0)

def score(job: Dict[str, Any]) -> float:
    """
    Calculate overall job quality score (0.0 to 1.0)
    
    Scoring factors:
    - Freshness (50%): Prefer jobs < 30 days old
    - Salary presence (20%): Bonus for having salary info
    - Company logo (10%): Bonus for having company logo
    - Description quality (20%): Based on length and structure
    """
    try:
        # 1. Freshness score (0.5 weight)
        days_old = job.get('daysOld') or calculate_days_old(job)
        freshness_score = max(0, (30 - days_old) / 30)  # Linear decay over 30 days
        
        # 2. Salary bonus (0.2 weight)
        salary_bonus = 0
        salary_fields = ['salary', 'salaryMin', 'salaryMax', 'compensation']
        for field in salary_fields:
            if field in job and job[field]:
                salary_amount = extract_salary_amount(str(job[field]))
                if salary_amount > 0:
                    salary_bonus = 0.2
                    break
        
        # 3. Logo bonus (0.1 weight)
        logo_bonus = 0.1 if has_company_logo(job) else 0
        
        # 4. Description quality (0.2 weight)
        description = job.get('description', '') or job.get('summary', '')
        description_score = calculate_description_quality(description) * 0.2
        
        # Combine all factors
        total_score = (
            freshness_score * 0.5 +
            salary_bonus +
            logo_bonus +
            description_score
        )
        
        # Ensure score is between 0 and 1
        final_score = max(0.0, min(1.0, total_score))
        
        logger.debug(f"Job quality score: {final_score:.3f} "
                    f"(freshness: {freshness_score:.2f}, "
                    f"salary: {salary_bonus}, "
                    f"logo: {logo_bonus}, "
                    f"description: {description_score:.2f})")
        
        return round(final_score, 3)
        
    except Exception as e:
        logger.error(f"Error calculating job quality score: {e}")
        return 0.5  # Default middle score

def score_and_sort_jobs(jobs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Add quality scores to jobs and sort by quality + relevance
    """
    try:
        # Add quality scores
        for job in jobs:
            job['qualityScore'] = score(job)
        
        # Sort by quality score (primary) and relevance score (secondary)
        jobs.sort(key=lambda j: (
            -j.get('qualityScore', 0),
            -j.get('relevanceScore', 0)
        ))
        
        logger.info(f"Scored and sorted {len(jobs)} jobs by quality")
        return jobs
        
    except Exception as e:
        logger.error(f"Error scoring and sorting jobs: {e}")
        return jobs

def get_quality_distribution(jobs: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Get quality score distribution for monitoring"""
    if not jobs:
        return {"count": 0, "average": 0, "distribution": {}}
    
    scores = [job.get('qualityScore', 0) for job in jobs]
    
    # Create distribution buckets
    buckets = {
        "0.0-0.2": 0,
        "0.2-0.4": 0, 
        "0.4-0.6": 0,
        "0.6-0.8": 0,
        "0.8-1.0": 0
    }
    
    for score in scores:
        if score < 0.2:
            buckets["0.0-0.2"] += 1
        elif score < 0.4:
            buckets["0.2-0.4"] += 1
        elif score < 0.6:
            buckets["0.4-0.6"] += 1
        elif score < 0.8:
            buckets["0.6-0.8"] += 1
        else:
            buckets["0.8-1.0"] += 1
    
    return {
        "count": len(jobs),
        "average": round(sum(scores) / len(scores), 3),
        "min": round(min(scores), 3),
        "max": round(max(scores), 3),
        "distribution": buckets
    }

# Quality thresholds for filtering
QUALITY_THRESHOLDS = {
    "excellent": 0.8,
    "good": 0.6,
    "fair": 0.4,
    "poor": 0.2
}

def filter_by_quality(jobs: List[Dict[str, Any]], min_quality: str = "fair") -> List[Dict[str, Any]]:
    """Filter jobs by minimum quality threshold"""
    threshold = QUALITY_THRESHOLDS.get(min_quality, 0.4)
    
    filtered_jobs = [job for job in jobs if job.get('qualityScore', 0) >= threshold]
    
    logger.info(f"Filtered {len(jobs)} jobs to {len(filtered_jobs)} with min quality '{min_quality}' ({threshold})")
    
    return filtered_jobs
