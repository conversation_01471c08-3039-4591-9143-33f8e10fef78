{"version": 3, "names": ["_react", "require", "_reactNativeReanimated", "_constants", "_utilities", "useScrollable", "scrollableRef", "useRef", "previousScrollableRef", "animatedScrollableType", "useSharedValue", "SCROLLABLE_TYPE", "UNDETERMINED", "animatedScrollableContentOffsetY", "animatedScrollableOverrideState", "SCROLLABLE_STATE", "isScrollableRefreshable", "setScrollableRef", "useCallback", "ref", "currentRefId", "current", "id", "removeScrollableRef", "findNodeHandle", "exports"], "sourceRoot": "../../../src", "sources": ["hooks/useScrollable.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEA,IAAAC,sBAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AAEA,IAAAG,UAAA,GAAAH,OAAA;AAEO,MAAMI,aAAa,GAAGA,CAAA,KAAM;EACjC;EACA,MAAMC,aAAa,GAAG,IAAAC,aAAM,EAAgB,IAAI,CAAC;EACjD,MAAMC,qBAAqB,GAAG,IAAAD,aAAM,EAAgB,IAAI,CAAC;;EAEzD;EACA,MAAME,sBAAsB,GAAG,IAAAC,qCAAc,EAC3CC,0BAAe,CAACC,YAClB,CAAC;EACD,MAAMC,gCAAgC,GAAG,IAAAH,qCAAc,EAAS,CAAC,CAAC;EAClE,MAAMI,+BAA+B,GAAG,IAAAJ,qCAAc,EACpDK,2BAAgB,CAACH,YACnB,CAAC;EACD,MAAMI,uBAAuB,GAAG,IAAAN,qCAAc,EAAU,KAAK,CAAC;;EAE9D;EACA,MAAMO,gBAAgB,GAAG,IAAAC,kBAAW,EAAEC,GAAkB,IAAK;IAC3D;IACA,MAAMC,YAAY,GAAGd,aAAa,CAACe,OAAO,EAAEC,EAAE,IAAI,IAAI;IAEtD,IAAIF,YAAY,KAAKD,GAAG,CAACG,EAAE,EAAE;MAC3B,IAAIhB,aAAa,CAACe,OAAO,EAAE;QACzB;QACAb,qBAAqB,CAACa,OAAO,GAAGf,aAAa,CAACe,OAAO;MACvD;MACA;MACAf,aAAa,CAACe,OAAO,GAAGF,GAAG;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,mBAAmB,GAAG,IAAAL,kBAAW,EAAEC,GAA0B,IAAK;IACtE;IACA,IAAIG,EAAqB;IACzB,IAAI;MACFA,EAAE,GAAG,IAAAE,yBAAc,EAACL,GAAG,CAACE,OAAO,CAAC;IAClC,CAAC,CAAC,MAAM;MACN;IACF;;IAEA;IACA,MAAMD,YAAY,GAAGd,aAAa,CAACe,OAAO,EAAEC,EAAE,IAAI,IAAI;;IAEtD;AACJ;AACA;AACA;AACA;IACI,IAAIA,EAAE,KAAKF,YAAY,EAAE;MACvB;MACAd,aAAa,CAACe,OAAO,GAAGb,qBAAqB,CAACa,OAAO;IACvD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLf,aAAa;IACbG,sBAAsB;IACtBI,gCAAgC;IAChCC,+BAA+B;IAC/BE,uBAAuB;IACvBC,gBAAgB;IAChBM;EACF,CAAC;AACH,CAAC;AAACE,OAAA,CAAApB,aAAA,GAAAA,aAAA", "ignoreList": []}