import type { BottomSheetFlatListMethods, BottomSheetFlatListProps } from './types';
declare const BottomSheetFlatList: import("react").MemoExoticComponent<import("react").ForwardRefExoticComponent<Omit<BottomSheetFlatListProps<never>, "ref"> & import("react").RefAttributes<BottomSheetFlatListMethods>>>;
declare const _default: <T>(props: BottomSheetFlatListProps<T>) => ReturnType<typeof BottomSheetFlatList>;
export default _default;
//# sourceMappingURL=BottomSheetFlatList.d.ts.map