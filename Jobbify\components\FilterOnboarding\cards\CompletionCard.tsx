import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { FontAwesome } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown, FadeIn } from 'react-native-reanimated';

interface CompletionCardProps {
  preferences: any;
  onNext: () => void;
  onPrevious?: () => void;
  theme: string;
  themeColors: any;
}

const { width } = Dimensions.get('window');

export default function CompletionCard({ 
  preferences, 
  onNext, 
  onPrevious, 
  theme, 
  themeColors 
}: CompletionCardProps) {
  
  const formatSalary = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getWorkStyleDisplay = (workStyle: string) => {
    switch (workStyle) {
      case 'remote': return 'Remote';
      case 'hybrid': return 'Hybrid';
      case 'in-person': return 'In-Person';
      default: return workStyle;
    }
  };

  const getExperienceLevelDisplay = (level: string) => {
    switch (level) {
      case 'entry': return 'Entry Level';
      case 'junior': return 'Junior';
      case 'mid': return 'Mid Level';
      case 'senior': return 'Senior';
      case 'lead': return 'Lead';
      case 'executive': return 'Executive';
      default: return level;
    }
  };

  return (
    <View style={styles.container}>
      <Animated.View entering={FadeInUp.delay(200)} style={styles.header}>
        <View style={styles.celebrationContainer}>
          <LinearGradient
            colors={['#10B981', '#059669']}
            style={styles.celebrationIcon}
          >
            <FontAwesome name="check" size={32} color="#FFFFFF" />
          </LinearGradient>
        </View>
        
        <Text style={[styles.title, { color: themeColors.text }]}>
          Perfect! You're all set!
        </Text>
        <Text style={[styles.subtitle, { color: themeColors.textSecondary }]}>
          We'll now show you personalized job matches based on your preferences
        </Text>
      </Animated.View>

      <Animated.View entering={FadeIn.delay(400)} style={styles.summaryContainer}>
        <Text style={[styles.summaryTitle, { color: themeColors.text }]}>
          Your Preferences Summary
        </Text>
        
        <View style={styles.summaryCards}>
          <View style={[styles.summaryCard, { backgroundColor: themeColors.card }]}>
            <FontAwesome name="map-marker" size={16} color="#3B82F6" />
            <Text style={[styles.summaryLabel, { color: themeColors.textSecondary }]}>
              Location
            </Text>
            <Text style={[styles.summaryValue, { color: themeColors.text }]}>
              {preferences.location || 'Not specified'}
            </Text>
          </View>

          <View style={[styles.summaryCard, { backgroundColor: themeColors.card }]}>
            <FontAwesome name="home" size={16} color="#10B981" />
            <Text style={[styles.summaryLabel, { color: themeColors.textSecondary }]}>
              Work Style
            </Text>
            <Text style={[styles.summaryValue, { color: themeColors.text }]}>
              {getWorkStyleDisplay(preferences.workStyle)}
            </Text>
          </View>

          <View style={[styles.summaryCard, { backgroundColor: themeColors.card }]}>
            <FontAwesome name="briefcase" size={16} color="#F59E0B" />
            <Text style={[styles.summaryLabel, { color: themeColors.textSecondary }]}>
              Job Types
            </Text>
            <Text style={[styles.summaryValue, { color: themeColors.text }]}>
              {preferences.jobTypes?.length || 0} selected
            </Text>
          </View>

          <View style={[styles.summaryCard, { backgroundColor: themeColors.card }]}>
            <FontAwesome name="star" size={16} color="#8B5CF6" />
            <Text style={[styles.summaryLabel, { color: themeColors.textSecondary }]}>
              Experience
            </Text>
            <Text style={[styles.summaryValue, { color: themeColors.text }]}>
              {getExperienceLevelDisplay(preferences.experienceLevel)}
            </Text>
          </View>

          <View style={[styles.summaryCard, { backgroundColor: themeColors.card }]}>
            <FontAwesome name="dollar" size={16} color="#EF4444" />
            <Text style={[styles.summaryLabel, { color: themeColors.textSecondary }]}>
              Salary Range
            </Text>
            <Text style={[styles.summaryValue, { color: themeColors.text }]}>
              {formatSalary(preferences.salaryRange?.min || 0)} - {formatSalary(preferences.salaryRange?.max || 0)}
            </Text>
          </View>

          <View style={[styles.summaryCard, { backgroundColor: themeColors.card }]}>
            <FontAwesome name="tags" size={16} color="#F97316" />
            <Text style={[styles.summaryLabel, { color: themeColors.textSecondary }]}>
              Categories
            </Text>
            <Text style={[styles.summaryValue, { color: themeColors.text }]}>
              {preferences.jobCategories?.length || 0} selected
            </Text>
          </View>
        </View>
      </Animated.View>

      <Animated.View entering={FadeInDown.delay(600)} style={styles.buttonContainer}>
        <View style={styles.buttonRow}>
          {onPrevious && (
            <TouchableOpacity
              style={[styles.backButton, { borderColor: themeColors.border }]}
              onPress={onPrevious}
            >
              <FontAwesome name="arrow-left" size={16} color={themeColors.textSecondary} />
              <Text style={[styles.backText, { color: themeColors.textSecondary }]}>
                Back
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[
              styles.completeButton,
              !onPrevious && styles.fullWidthButton
            ]}
            onPress={onNext}
          >
            <LinearGradient
              colors={['#10B981', '#059669']}
              style={styles.completeGradient}
            >
              <Text style={styles.completeText}>Start Finding Jobs!</Text>
              <FontAwesome name="rocket" size={16} color="#FFFFFF" />
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
    paddingTop: 32,
  },
  celebrationContainer: {
    marginBottom: 24,
  },
  celebrationIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: 16,
  },
  summaryContainer: {
    flex: 1,
    marginBottom: 24,
  },
  summaryTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  summaryCards: {
    gap: 12,
  },
  summaryCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    gap: 12,
  },
  summaryLabel: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  buttonContainer: {
    paddingBottom: 32,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 2,
    gap: 8,
    flex: 1,
  },
  backText: {
    fontSize: 16,
    fontWeight: '600',
  },
  completeButton: {
    flex: 2,
    borderRadius: 12,
    overflow: 'hidden',
  },
  fullWidthButton: {
    flex: 1,
  },
  completeGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    gap: 8,
  },
  completeText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
