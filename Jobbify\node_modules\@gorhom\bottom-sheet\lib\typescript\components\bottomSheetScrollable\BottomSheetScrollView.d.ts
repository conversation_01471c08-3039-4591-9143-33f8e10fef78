import type { BottomSheetScrollViewMethods, BottomSheetScrollViewProps } from './types';
declare const BottomSheetScrollView: import("react").MemoExoticComponent<import("react").ForwardRefExoticComponent<Omit<BottomSheetScrollViewProps, "ref"> & import("react").RefAttributes<BottomSheetScrollViewMethods>>>;
declare const _default: (props: BottomSheetScrollViewProps) => ReturnType<typeof BottomSheetScrollView>;
export default _default;
//# sourceMappingURL=BottomSheetScrollView.d.ts.map