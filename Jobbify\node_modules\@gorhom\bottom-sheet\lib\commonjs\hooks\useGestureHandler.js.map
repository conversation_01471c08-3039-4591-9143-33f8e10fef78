{"version": 3, "names": ["_reactNativeGestureHandler", "require", "_reactNativeReanimated", "_constants", "useGestureHandler", "source", "state", "gestureSource", "onStart", "onChange", "onEnd", "onFinalize", "handleOnStart", "useWorkletCallback", "event", "value", "State", "BEGAN", "handleOnChange", "handleOnEnd", "GESTURE_SOURCE", "UNDETERMINED", "handleOnFinalize", "exports"], "sourceRoot": "../../../src", "sources": ["hooks/useGestureHandler.ts"], "mappings": ";;;;;;AAAA,IAAAA,0BAAA,GAAAC,OAAA;AAQA,IAAAC,sBAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AAMO,MAAMG,iBAA0C,GAAGA,CACxDC,MAAsB,EACtBC,KAAyB,EACzBC,aAA0C,EAC1CC,OAAwC,EACxCC,QAAyC,EACzCC,KAAsC,EACtCC,UAA2C,KACxC;EACH,MAAMC,aAAa,GAAG,IAAAC,yCAAkB,EACrCC,KAA6D,IAAK;IACjER,KAAK,CAACS,KAAK,GAAGC,gCAAK,CAACC,KAAK;IACzBV,aAAa,CAACQ,KAAK,GAAGV,MAAM;IAE5BG,OAAO,CAACH,MAAM,EAAES,KAAK,CAAC;IACtB;EACF,CAAC,EACD,CAACR,KAAK,EAAEC,aAAa,EAAEF,MAAM,EAAEG,OAAO,CACxC,CAAC;EAED,MAAMU,cAAc,GAAG,IAAAL,yCAAkB,EAErCC,KAEC,IACE;IACH,IAAIP,aAAa,CAACQ,KAAK,KAAKV,MAAM,EAAE;MAClC;IACF;IAEAC,KAAK,CAACS,KAAK,GAAGD,KAAK,CAACR,KAAK;IACzBG,QAAQ,CAACJ,MAAM,EAAES,KAAK,CAAC;EACzB,CAAC,EACD,CAACR,KAAK,EAAEC,aAAa,EAAEF,MAAM,EAAEI,QAAQ,CACzC,CAAC;EAED,MAAMU,WAAW,GAAG,IAAAN,yCAAkB,EACnCC,KAA6D,IAAK;IACjE,IAAIP,aAAa,CAACQ,KAAK,KAAKV,MAAM,EAAE;MAClC;IACF;IAEAC,KAAK,CAACS,KAAK,GAAGD,KAAK,CAACR,KAAK;IACzBC,aAAa,CAACQ,KAAK,GAAGK,yBAAc,CAACC,YAAY;IAEjDX,KAAK,CAACL,MAAM,EAAES,KAAK,CAAC;EACtB,CAAC,EACD,CAACR,KAAK,EAAEC,aAAa,EAAEF,MAAM,EAAEK,KAAK,CACtC,CAAC;EAED,MAAMY,gBAAgB,GAAG,IAAAT,yCAAkB,EACxCC,KAA6D,IAAK;IACjE,IAAIP,aAAa,CAACQ,KAAK,KAAKV,MAAM,EAAE;MAClC;IACF;IAEAC,KAAK,CAACS,KAAK,GAAGD,KAAK,CAACR,KAAK;IACzBC,aAAa,CAACQ,KAAK,GAAGK,yBAAc,CAACC,YAAY;IAEjDV,UAAU,CAACN,MAAM,EAAES,KAAK,CAAC;EAC3B,CAAC,EACD,CAACR,KAAK,EAAEC,aAAa,EAAEF,MAAM,EAAEM,UAAU,CAC3C,CAAC;EAED,OAAO;IACLC,aAAa;IACbM,cAAc;IACdC,WAAW;IACXG;EACF,CAAC;AACH,CAAC;AAACC,OAAA,CAAAnB,iBAAA,GAAAA,iBAAA", "ignoreList": []}