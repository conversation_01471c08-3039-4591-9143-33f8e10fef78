import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Dimensions,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import { router } from 'expo-router';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface UpgradeModalProps {
  visible: boolean;
  onClose: () => void;
  searchesUsed: number;
  searchLimit: number;
  onUpgrade?: (plan: 'plus' | 'pro') => void;
}

export const UpgradeModal: React.FC<UpgradeModalProps> = ({
  visible,
  onClose,
  searchesUsed,
  searchLimit,
  onUpgrade
}) => {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;

  const plans = [
    {
      id: 'plus' as const,
      name: 'Plus',
      price: '$9.99',
      period: '/month',
      searches: '50 searches/day',
      features: [
        'Fresh job searches',
        'Priority API access',
        'Advanced filters',
        'Email notifications'
      ],
      color: '#4CAF50',
      popular: false
    },
    {
      id: 'pro' as const,
      name: 'Pro',
      price: '$19.99',
      period: '/month',
      searches: '150 searches/day',
      features: [
        'Unlimited fresh searches',
        'Premium API access',
        'AI job matching',
        'Custom alerts',
        'Priority support'
      ],
      color: '#2196F3',
      popular: true
    }
  ];

  const handleUpgrade = (planId: 'plus' | 'pro') => {
    onUpgrade?.(planId);
    onClose();
    // Navigate to paywall screen
    router.push('/(paywall)/subscription');
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={[styles.modalContainer, { backgroundColor: themeColors.background }]}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={[styles.closeButton, { backgroundColor: themeColors.card }]}
              onPress={onClose}
            >
              <MaterialIcons name="close" size={24} color={themeColors.text} />
            </TouchableOpacity>
            
            <View style={styles.headerContent}>
              <MaterialIcons name="trending-up" size={48} color="#FF6B6B" />
              <Text style={[styles.title, { color: themeColors.text }]}>
                Daily Search Limit Reached
              </Text>
              <Text style={[styles.subtitle, { color: themeColors.textSecondary }]}>
                You've used {searchesUsed} of {searchLimit} free searches today
              </Text>
            </View>
          </View>

          {/* Plans */}
          <ScrollView style={styles.plansContainer} showsVerticalScrollIndicator={false}>
            <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
              Upgrade for Fresh Job Searches
            </Text>
            
            {plans.map((plan) => (
              <TouchableOpacity
                key={plan.id}
                style={[
                  styles.planCard,
                  { 
                    backgroundColor: themeColors.card,
                    borderColor: plan.popular ? plan.color : themeColors.border,
                    borderWidth: plan.popular ? 2 : 1
                  }
                ]}
                onPress={() => handleUpgrade(plan.id)}
              >
                {plan.popular && (
                  <View style={[styles.popularBadge, { backgroundColor: plan.color }]}>
                    <Text style={styles.popularText}>MOST POPULAR</Text>
                  </View>
                )}
                
                <View style={styles.planHeader}>
                  <Text style={[styles.planName, { color: themeColors.text }]}>
                    {plan.name}
                  </Text>
                  <View style={styles.priceContainer}>
                    <Text style={[styles.price, { color: plan.color }]}>
                      {plan.price}
                    </Text>
                    <Text style={[styles.period, { color: themeColors.textSecondary }]}>
                      {plan.period}
                    </Text>
                  </View>
                </View>

                <Text style={[styles.searches, { color: themeColors.textSecondary }]}>
                  {plan.searches}
                </Text>

                <View style={styles.featuresContainer}>
                  {plan.features.map((feature, index) => (
                    <View key={index} style={styles.featureRow}>
                      <MaterialIcons 
                        name="check-circle" 
                        size={16} 
                        color={plan.color} 
                        style={styles.checkIcon}
                      />
                      <Text style={[styles.featureText, { color: themeColors.text }]}>
                        {feature}
                      </Text>
                    </View>
                  ))}
                </View>

                <LinearGradient
                  colors={[plan.color, plan.color + '80']}
                  style={styles.upgradeButton}
                >
                  <Text style={styles.upgradeButtonText}>
                    Upgrade to {plan.name}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            ))}

            <View style={styles.footer}>
              <Text style={[styles.footerText, { color: themeColors.textSecondary }]}>
                • Cancel anytime{'\n'}
                • 7-day free trial{'\n'}
                • Instant access to fresh job searches
              </Text>
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    height: SCREEN_HEIGHT * 0.85,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  closeButton: {
    position: 'absolute',
    top: 0,
    right: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  headerContent: {
    alignItems: 'center',
    paddingTop: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  plansContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 24,
  },
  planCard: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    position: 'relative',
  },
  popularBadge: {
    position: 'absolute',
    top: -8,
    left: 20,
    right: 20,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  popularText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '700',
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
    marginTop: 8,
  },
  planName: {
    fontSize: 24,
    fontWeight: '700',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  price: {
    fontSize: 28,
    fontWeight: '700',
  },
  period: {
    fontSize: 16,
    marginLeft: 4,
  },
  searches: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 16,
  },
  featuresContainer: {
    marginBottom: 20,
  },
  featureRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  checkIcon: {
    marginRight: 8,
  },
  featureText: {
    fontSize: 16,
    flex: 1,
  },
  upgradeButton: {
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  upgradeButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  footer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});
