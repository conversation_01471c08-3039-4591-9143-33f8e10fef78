{"version": 3, "names": ["_react", "require", "_reactNativeReanimated", "_constants", "_findNodeHandle", "_useBottomSheetInternal", "useScrollHandler", "_", "onScroll", "scrollableRef", "useRef", "scrollableContentOffsetY", "useSharedValue", "animatedScrollableState", "animatedAnimationState", "animatedScrollableContentOffsetY", "useBottomSheetInternal", "useEffect", "element", "findNodeHandle", "current", "scrollOffset", "supportsPassive", "maybePrevent", "lastTouchY", "initialContentOffsetY", "shouldLockInitialPosition", "handleOnTouchStart", "event", "touches", "length", "scrollTop", "clientY", "handleOnTouchMove", "value", "SCROLLABLE_STATE", "LOCKED", "cancelable", "preventDefault", "touchY", "touchYDelta", "handleOnTouchEnd", "lockPosition", "scroll", "top", "left", "behavior", "handleOnScroll", "ANIMATION_STATE", "RUNNING", "Math", "max", "stopPropagation", "window", "addEventListener", "passive", "_e", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "exports"], "sourceRoot": "../../../src", "sources": ["hooks/useScrollHandler.web.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AAEA,IAAAG,eAAA,GAAAH,OAAA;AACA,IAAAI,uBAAA,GAAAJ,OAAA;AAOO,MAAMK,gBAAgB,GAAGA,CAACC,CAAQ,EAAEC,QAA0B,KAAK;EACxE;EACA,MAAMC,aAAa,GAAG,IAAAC,aAAM,EAAa,IAAI,CAAC;EAC9C;;EAEA;EACA,MAAMC,wBAAwB,GAAG,IAAAC,qCAAc,EAAS,CAAC,CAAC;EAC1D;;EAEA;EACA,MAAM;IACJC,uBAAuB;IACvBC,sBAAsB;IACtBC;EACF,CAAC,GAAG,IAAAC,8CAAsB,EAAC,CAAC;EAC5B;;EAEA;EACA,IAAAC,gBAAS,EAAC,MAAM;IACd;IACA,MAAMC,OAAO,GAAG,IAAAC,8BAAc,EAACV,aAAa,CAACW,OAAO,CAAQ;IAC5D,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,eAAe,GAAG,KAAK;IAC3B,IAAIC,YAAY,GAAG,KAAK;IACxB,IAAIC,UAAU,GAAG,CAAC;IAElB,IAAIC,qBAAqB,GAAG,CAAC;IAC7B,MAAMC,yBAAyB,GAAG,KAAK;IAEvC,SAASC,kBAAkBA,CAACC,KAAiB,EAAE;MAC7C,IAAIA,KAAK,CAACC,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QAC9B;MACF;MAEAL,qBAAqB,GAAGP,OAAO,CAACa,SAAS;MACzCP,UAAU,GAAGI,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACG,OAAO;MACrCT,YAAY,GAAGF,YAAY,IAAI,CAAC;IAClC;IAEA,SAASY,iBAAiBA,CAACL,KAAiB,EAAE;MAC5C,IAAIf,uBAAuB,CAACqB,KAAK,KAAKC,2BAAgB,CAACC,MAAM,IAAIR,KAAK,CAACS,UAAU,EAAE;QACjF,OAAOT,KAAK,CAACU,cAAc,CAAC,CAAC;MAC/B;MAEA,IAAIf,YAAY,EAAE;QAChBA,YAAY,GAAG,KAAK;QAEpB,MAAMgB,MAAM,GAAGX,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACG,OAAO;QACvC,MAAMQ,WAAW,GAAGD,MAAM,GAAGf,UAAU;QAEvC,IAAIgB,WAAW,GAAG,CAAC,IAAIZ,KAAK,CAACS,UAAU,EAAE;UACvC,OAAOT,KAAK,CAACU,cAAc,CAAC,CAAC;QAC/B;MACF;MAEA,OAAO,IAAI;IACb;IAEA,SAASG,gBAAgBA,CAAA,EAAG;MAC1B,IAAI5B,uBAAuB,CAACqB,KAAK,KAAKC,2BAAgB,CAACC,MAAM,EAAE;QAC7D,MAAMM,YAAY,GAAGhB,yBAAyB,GACzCD,qBAAqB,IAAI,CAAC,GAC3B,CAAC;QACLP,OAAO,CAACyB,MAAM,CAAC;UACbC,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFnC,wBAAwB,CAACuB,KAAK,GAAGQ,YAAY;QAC7C;MACF;IACF;IAEA,SAASK,cAAcA,CAACnB,KAAiB,EAAE;MACzCP,YAAY,GAAGH,OAAO,CAACa,SAAS;MAEhC,IAAIjB,sBAAsB,CAACoB,KAAK,KAAKc,0BAAe,CAACC,OAAO,EAAE;QAC5DtC,wBAAwB,CAACuB,KAAK,GAAGgB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE9B,YAAY,CAAC;QAC1DN,gCAAgC,CAACmB,KAAK,GAAGgB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE9B,YAAY,CAAC;MACpE;MAEA,IAAIA,YAAY,IAAI,CAAC,IAAIO,KAAK,CAACS,UAAU,EAAE;QACzCT,KAAK,CAACU,cAAc,CAAC,CAAC;QACtBV,KAAK,CAACwB,eAAe,CAAC,CAAC;QACvB,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb;IAEA,IAAI;MACF;MACAC,MAAM,CAACC,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAE;QACpC;QACA;QACA,IAAIC,OAAOA,CAAA,EAAG;UACZjC,eAAe,GAAG,IAAI;QACxB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOkC,EAAE,EAAE,CAAC;IAEdtC,OAAO,CAACoC,gBAAgB,CACtB,YAAY,EACZ3B,kBAAkB,EAClBL,eAAe,GACX;MACEiC,OAAO,EAAE;IACX,CAAC,GACD,KACN,CAAC;IAEDrC,OAAO,CAACoC,gBAAgB,CACtB,WAAW,EACXrB,iBAAiB,EACjBX,eAAe,GACX;MACEiC,OAAO,EAAE;IACX,CAAC,GACD,KACN,CAAC;IAEDrC,OAAO,CAACoC,gBAAgB,CACtB,UAAU,EACVb,gBAAgB,EAChBnB,eAAe,GACX;MACEiC,OAAO,EAAE;IACX,CAAC,GACD,KACN,CAAC;IAEDrC,OAAO,CAACoC,gBAAgB,CACtB,QAAQ,EACRP,cAAc,EACdzB,eAAe,GACX;MACEiC,OAAO,EAAE;IACX,CAAC,GACD,KACN,CAAC;IAED,OAAO,MAAM;MACX;MACAF,MAAM,CAACI,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;MACxCvC,OAAO,CAACuC,mBAAmB,CAAC,YAAY,EAAE9B,kBAAkB,CAAC;MAC7DT,OAAO,CAACuC,mBAAmB,CAAC,WAAW,EAAExB,iBAAiB,CAAC;MAC3Df,OAAO,CAACuC,mBAAmB,CAAC,UAAU,EAAEhB,gBAAgB,CAAC;MACzDvB,OAAO,CAACuC,mBAAmB,CAAC,QAAQ,EAAEV,cAAc,CAAC;IACvD,CAAC;EACH,CAAC,EAAE,CACDjC,sBAAsB,EACtBC,gCAAgC,EAChCF,uBAAuB,EACvBF,wBAAwB,CACzB,CAAC;EACF;;EAEA,OAAO;IACL+C,aAAa,EAAElD,QAAQ;IACvBC,aAAa;IACbE;EACF,CAAC;AACH,CAAC;AAACgD,OAAA,CAAArD,gBAAA,GAAAA,gBAAA", "ignoreList": []}