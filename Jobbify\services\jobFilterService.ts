/**
 * Enhanced Job Filter Service
 * Handles job filtering logic with improved location filtering for US-based users
 */

import { Job } from '@/context/AppContext';
import { UserJobPreferences } from './jobRecommendationService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { filterJobsByLocationAndRadius, geocodeLocation } from './LocationService';

export interface JobFilters {
  // Location filters
  location: string;
  maxDistance: number; // radius in miles: 5, 10, 25, 50, 100+
  remoteOnly: boolean;
  inPersonOnly: boolean;

  // Employment type filters
  employmentTypes: string[]; // Internship, Full-time Job, Part-time Job, Contract, Freelance, Remote

  // Job category filters
  jobCategories: string[]; // Software Engineer, Medical Intern, Data Scientist, Product Manager, etc.

  // Experience level filters
  experienceLevel: string[];

  // Salary filters
  minSalary: number;
  maxSalary: number;

  // Company filters
  companySize: string[];
  industries: string[];

  // Other filters
  postedWithin: number; // days
  hasLogo: boolean;

  // Quick filters (simplified)
  quickFilters: {
    remote: boolean;
    recentlyPosted: boolean;
    hasLogo: boolean;
  };
}

// Available employment types
export const EMPLOYMENT_TYPES = [
  'Internship',
  'Full-time Job',
  'Part-time Job',
  'Contract',
  'Freelance',
  'Remote'
];

// Available job categories
export const JOB_CATEGORIES = [
  'Software Engineer',
  'Medical Intern',
  'Data Scientist',
  'Product Manager',
  'Marketing',
  'Sales',
  'Design',
  'Engineering',
  'Operations',
  'Finance',
  'Healthcare',
  'Education',
  'Customer Service',
  'Human Resources',
  'Legal',
  'Research',
  'Consulting'
];

// Available radius options
export const RADIUS_OPTIONS = [5, 10, 25, 50, 100];

export const defaultFilters: JobFilters = {
  location: 'Hayward', // Default to Hayward for US-based filtering
  maxDistance: 50,
  remoteOnly: false,
  inPersonOnly: false,
  employmentTypes: [],
  jobCategories: [],
  experienceLevel: [],
  minSalary: 0,
  maxSalary: 500000,
  companySize: [],
  industries: [],
  postedWithin: 30,
  hasLogo: false,
  quickFilters: {
    remote: false,
    recentlyPosted: false,
    hasLogo: false,
  }
};

/**
 * Enhanced location filtering for US-based users
 */
const isUSLocation = (location: string): boolean => {
  const usStates = [
    'alabama', 'alaska', 'arizona', 'arkansas', 'california', 'colorado', 'connecticut', 'delaware',
    'florida', 'georgia', 'hawaii', 'idaho', 'illinois', 'indiana', 'iowa', 'kansas', 'kentucky',
    'louisiana', 'maine', 'maryland', 'massachusetts', 'michigan', 'minnesota', 'mississippi',
    'missouri', 'montana', 'nebraska', 'nevada', 'new hampshire', 'new jersey', 'new mexico',
    'new york', 'north carolina', 'north dakota', 'ohio', 'oklahoma', 'oregon', 'pennsylvania',
    'rhode island', 'south carolina', 'south dakota', 'tennessee', 'texas', 'utah', 'vermont',
    'virginia', 'washington', 'west virginia', 'wisconsin', 'wyoming'
  ];
  
  const usCities = [
    'san francisco', 'new york', 'los angeles', 'chicago', 'houston', 'phoenix', 'philadelphia',
    'san antonio', 'san diego', 'dallas', 'san jose', 'austin', 'jacksonville', 'fort worth',
    'columbus', 'charlotte', 'seattle', 'denver', 'boston', 'detroit', 'nashville', 'memphis',
    'portland', 'oklahoma city', 'las vegas', 'baltimore', 'milwaukee', 'albuquerque', 'tucson',
    'fresno', 'sacramento', 'mesa', 'kansas city', 'atlanta', 'long beach', 'colorado springs',
    'raleigh', 'miami', 'virginia beach', 'omaha', 'oakland', 'minneapolis', 'tulsa', 'arlington',
    'tampa', 'new orleans', 'wichita', 'cleveland', 'bakersfield', 'aurora', 'anaheim', 'honolulu',
    'santa ana', 'riverside', 'corpus christi', 'lexington', 'stockton', 'henderson', 'saint paul',
    'st. louis', 'cincinnati', 'pittsburgh', 'greensboro', 'lincoln', 'plano', 'anchorage',
    'buffalo', 'fort wayne', 'jersey city', 'chula vista', 'orlando', 'norfolk', 'chandler',
    'laredo', 'madison', 'lubbock', 'winston-salem', 'garland', 'glendale', 'hialeah', 'reno',
    'baton rouge', 'irvine', 'chesapeake', 'irving', 'scottsdale', 'north las vegas', 'fremont',
    'gilbert', 'san bernardino', 'boise', 'birmingham', 'hayward'
  ];
  
  const locationLower = location.toLowerCase();
  
  // Check for US states
  if (usStates.some(state => locationLower.includes(state))) {
    return true;
  }
  
  // Check for US cities
  if (usCities.some(city => locationLower.includes(city))) {
    return true;
  }
  
  // Check for common US location patterns
  if (locationLower.includes(', ca') || locationLower.includes(', california') ||
      locationLower.includes(', ny') || locationLower.includes(', new york') ||
      locationLower.includes(', tx') || locationLower.includes(', texas') ||
      locationLower.includes(', fl') || locationLower.includes(', florida') ||
      locationLower.includes('united states') || locationLower.includes('usa') ||
      locationLower.includes('us ')) {
    return true;
  }
  
  return false;
};

/**
 * Apply filters to a list of jobs with enhanced location filtering
 */
export const applyFilters = (jobs: Job[], filters: JobFilters, skipLocationFiltering: boolean = false): Job[] => {
  console.log(`[FILTER] Starting with ${jobs.length} jobs, filters:`, {
    location: filters.location,
    remoteOnly: filters.remoteOnly,
    employmentTypes: filters.employmentTypes,
    jobCategories: filters.jobCategories,
    quickFilters: filters.quickFilters
  });
  
  return jobs.filter(job => {
    // Define isRemoteJob for use throughout the filter
    const isRemoteJob = job.location.toLowerCase().includes('remote') ||
                       job.location.toLowerCase().includes('anywhere') ||
                       job.tags.some(tag => tag.toLowerCase().includes('remote'));

    // First, apply work style filters (remote/in-person) - these take priority
    // But skip if location filtering has already been handled
    if (!skipLocationFiltering) {
      // If user wants remote only, exclude non-remote jobs
      if (filters.remoteOnly && !isRemoteJob) {
        return false;
      }

      // If user wants in-person only, exclude remote jobs
      if (filters.inPersonOnly && isRemoteJob) {
        return false;
      }
    }

    // Skip location filtering if it's already been handled by enhanced location filtering
    if (!skipLocationFiltering) {
      // Enhanced location filtering - prioritize US locations for Hayward users
      if (filters.location && filters.location.toLowerCase().includes('hayward')) {
        const jobLocation = job.location.toLowerCase();

        // Only include remote jobs if not filtering for in-person only
        if (isRemoteJob && !filters.inPersonOnly) {
          return true;
        }

        // For Hayward users, filter out non-US locations (unless remote and allowed)
        if (!isUSLocation(job.location) && !isRemoteJob) {
          console.log(`[FILTER] Filtering out non-US job: ${job.title} at ${job.location}`);
          return false;
        }

        // Prefer Bay Area and California jobs
        if (jobLocation.includes('california') || jobLocation.includes('ca') ||
            jobLocation.includes('san francisco') || jobLocation.includes('bay area') ||
            jobLocation.includes('hayward') || jobLocation.includes('oakland') ||
            jobLocation.includes('fremont') || jobLocation.includes('san jose')) {
          return true;
        }

        // Include other US locations (but not remote if in-person only)
        return isUSLocation(job.location) && (!isRemoteJob || !filters.inPersonOnly);
      }

      // General location filter
      if (filters.location && !filters.remoteOnly) {
        const jobLocation = job.location.toLowerCase();
        const filterLocation = filters.location.toLowerCase();

        // Only include remote jobs if not filtering for in-person only
        if (isRemoteJob && !filters.inPersonOnly) {
          return true;
        }

        // Skip remote jobs if user wants in-person only
        if (isRemoteJob && filters.inPersonOnly) {
          return false;
        }

        if (!jobLocation.includes(filterLocation)) {
          return false;
        }
      }
    }
    
    // Remote only filter
    if (filters.remoteOnly) {
      const isRemote = job.location.toLowerCase().includes('remote') ||
                      job.location.toLowerCase().includes('anywhere') ||
                      job.tags.some(tag => tag.toLowerCase().includes('remote'));
      if (!isRemote) {
        return false;
      }
    }

    // In-person only filter
    if (filters.inPersonOnly) {
      const isRemote = job.location.toLowerCase().includes('remote') ||
                      job.location.toLowerCase().includes('anywhere') ||
                      job.tags.some(tag => tag.toLowerCase().includes('remote'));
      if (isRemote) {
        return false;
      }
    }
    
    // Employment type filter - more lenient approach
    if (filters.employmentTypes.length > 0) {
      // If no specific employment types are selected, or if "Full-time" is selected, be very lenient
      const hasFullTime = filters.employmentTypes.some(type => type.toLowerCase().includes('full'));
      const hasInternship = filters.employmentTypes.some(type => type.toLowerCase().includes('intern'));
      const hasContract = filters.employmentTypes.some(type => type.toLowerCase().includes('contract'));
      const hasPartTime = filters.employmentTypes.some(type => type.toLowerCase().includes('part'));
      const hasFreelance = filters.employmentTypes.some(type => type.toLowerCase().includes('freelance'));

      // Check if job matches any of the selected types
      const jobTitle = job.title.toLowerCase();
      const jobTags = job.tags.map(tag => tag.toLowerCase()).join(' ');
      const jobType = (job.type || '').toLowerCase();
      const jobDescription = (job.description || '').toLowerCase();

      let employmentTypeMatch = false;

      // Check for internship
      if (hasInternship && (
        jobTitle.includes('intern') ||
        jobTags.includes('intern') ||
        jobType.includes('intern') ||
        jobDescription.includes('intern')
      )) {
        employmentTypeMatch = true;
      }

      // Check for contract
      if (hasContract && (
        jobTitle.includes('contract') ||
        jobTags.includes('contract') ||
        jobType.includes('contract') ||
        jobDescription.includes('contract')
      )) {
        employmentTypeMatch = true;
      }

      // Check for part-time
      if (hasPartTime && (
        jobTitle.includes('part') ||
        jobTags.includes('part') ||
        jobType.includes('part') ||
        jobDescription.includes('part-time')
      )) {
        employmentTypeMatch = true;
      }

      // Check for freelance
      if (hasFreelance && (
        jobTitle.includes('freelance') ||
        jobTags.includes('freelance') ||
        jobType.includes('freelance') ||
        jobDescription.includes('freelance')
      )) {
        employmentTypeMatch = true;
      }

      // For full-time or if no specific indicators found, be very lenient
      if (hasFullTime || !employmentTypeMatch) {
        // If it's not clearly an internship, contract, part-time, or freelance, assume it could be full-time
        const isNotOtherType = !jobTitle.includes('intern') &&
                              !jobTitle.includes('contract') &&
                              !jobTitle.includes('part-time') &&
                              !jobTitle.includes('freelance');
        if (isNotOtherType || hasFullTime) {
          employmentTypeMatch = true;
        }
      }

      if (!employmentTypeMatch) {
        console.log(`[FILTER] Job "${job.title}" filtered out - employment type mismatch. Required: ${filters.employmentTypes.join(',')}, Job tags: ${job.tags.join(',')}, Job type: ${job.type}`);
        return false;
      }
    }

    // Job category filter
    if (filters.jobCategories.length > 0) {
      const categoryMatch = filters.jobCategories.some((category: string) => {
        const categoryLower = category.toLowerCase();
        return job.tags.some(tag => tag.toLowerCase().includes(categoryLower)) ||
               job.title.toLowerCase().includes(categoryLower) ||
               job.description.toLowerCase().includes(categoryLower);
      });
      if (!categoryMatch) {
        console.log(`[FILTER] Job "${job.title}" filtered out - category mismatch. Required: ${filters.jobCategories}, Job tags: ${job.tags}`);
        return false;
      }
    }
    
    // Enhanced salary filter
    if (filters.minSalary > 0 || filters.maxSalary < 500000) {
      const salaryMatch = job.pay.match(/\$?(\d+(?:,\d+)?(?:k|K)?)\s*-?\s*\$?(\d+(?:,\d+)?(?:k|K)?)?/);
      if (salaryMatch) {
        const parseAmount = (amount: string): number => {
          const cleaned = amount.replace(/[,$]/g, '');
          const num = parseInt(cleaned);
          return cleaned.toLowerCase().includes('k') ? num * 1000 : num;
        };
        
        const minSalary = parseAmount(salaryMatch[1]);
        const maxSalary = salaryMatch[2] ? parseAmount(salaryMatch[2]) : minSalary;
        
        if (maxSalary < filters.minSalary || minSalary > filters.maxSalary) {
          return false;
        }
      }
    }
    
    // Industry filter
    if (filters.industries.length > 0) {
      const industryMatch = filters.industries.some(industry =>
        job.tags.some(tag => tag.toLowerCase().includes(industry.toLowerCase())) ||
        job.company.toLowerCase().includes(industry.toLowerCase()) ||
        job.description.toLowerCase().includes(industry.toLowerCase())
      );
      if (!industryMatch) {
        return false;
      }
    }
    
    // Logo filter
    if (filters.hasLogo && !job.logo && !job.image) {
      return false;
    }
    
    // Quick filters
    if (filters.quickFilters.remote) {
      const isRemote = job.location.toLowerCase().includes('remote') ||
                      job.location.toLowerCase().includes('anywhere') ||
                      job.tags.some(tag => tag.toLowerCase().includes('remote'));
      if (!isRemote) {
        return false;
      }
    }
    
    // Logo filter (moved from hasLogo property to quick filter)
    if (filters.quickFilters.hasLogo && !job.logo && !job.image) {
      return false;
    }
    
    if (filters.quickFilters.recentlyPosted) {
      if (job.postedDate) {
        const postedDate = new Date(job.postedDate);
        const daysSincePosted = (Date.now() - postedDate.getTime()) / (1000 * 60 * 60 * 24);
        if (daysSincePosted > 7) {
          return false;
        }
      }
    }

    return true;
  });
};

/**
 * Apply filters with enhanced location-based filtering using geocoding
 */
export const applyFiltersWithLocation = async (
  jobs: Job[],
  filters: JobFilters,
  userCoordinates?: { latitude: number; longitude: number }
): Promise<Job[]> => {
  console.log(`[FILTER] Starting enhanced filtering with ${jobs.length} jobs`);

  let filteredJobs = jobs;

  // First apply location-based filtering if location is specified
  if (filters.location && filters.location.trim() !== '') {
    console.log(`[FILTER] Applying location filter: ${filters.location}, radius: ${filters.maxDistance} miles`);

    // Determine if remote jobs should be included
    const includeRemote = !filters.inPersonOnly;

    filteredJobs = await filterJobsByLocationAndRadius(
      filteredJobs,
      filters.location,
      userCoordinates || null,
      filters.maxDistance,
      includeRemote
    );

    console.log(`[FILTER] After location filtering: ${filteredJobs.length} jobs`);
  }

  // Then apply all other filters using the existing logic, but skip location filtering since we already handled it
  filteredJobs = applyFilters(filteredJobs, filters, true);

  console.log(`[FILTER] Final filtered jobs: ${filteredJobs.length}`);
  return filteredJobs;
};

/**
 * Convert user preferences to filters
 */
export const preferencesToFilters = (preferences: UserJobPreferences): JobFilters => {
  return {
    location: preferences.preferred_locations[0] || '',
    maxDistance: preferences.max_commute_distance,
    remoteOnly: preferences.remote_work_preference === 'required',
    inPersonOnly: preferences.remote_work_preference === 'not_preferred',
    employmentTypes: preferences.preferred_job_types,
    jobCategories: [],
    experienceLevel: [preferences.experience_level],
    minSalary: preferences.min_salary || 0,
    maxSalary: preferences.max_salary || 500000,
    companySize: preferences.preferred_company_sizes,
    industries: preferences.preferred_industries,
    postedWithin: 30,
    hasLogo: false,
    quickFilters: {
      remote: preferences.remote_work_preference === 'required',
      recentlyPosted: false,
      hasLogo: (preferences.min_salary || 0) >= 100000, // Use hasLogo for high-pay preference
    }
  };
};

/**
 * Save user's current filter settings
 */
export const saveFilterSettings = async (userId: string, filters: JobFilters): Promise<void> => {
  try {
    await AsyncStorage.setItem(`job_filters_${userId}`, JSON.stringify(filters));
  } catch (error) {
    console.error('Error saving filter settings:', error);
  }
};

/**
 * Load user's saved filter settings
 */
export const loadFilterSettings = async (userId: string): Promise<JobFilters> => {
  try {
    const saved = await AsyncStorage.getItem(`job_filters_${userId}`);
    if (saved) {
      return { ...defaultFilters, ...JSON.parse(saved) };
    }
  } catch (error) {
    console.error('Error loading filter settings:', error);
  }
  return defaultFilters;
};

/**
 * Get filter summary for display
 */
export const getFilterSummary = (filters: JobFilters): string => {
  const activeFilters: string[] = [];

  if (filters.location) {
    activeFilters.push(`Location: ${filters.location}`);
  }

  if (filters.remoteOnly) {
    activeFilters.push('Remote only');
  }

  if (filters.inPersonOnly) {
    activeFilters.push('In-person only');
  }

  if (filters.employmentTypes.length > 0) {
    activeFilters.push(`Employment: ${filters.employmentTypes.join(', ')}`);
  }

  if (filters.jobCategories.length > 0) {
    activeFilters.push(`Categories: ${filters.jobCategories.join(', ')}`);
  }

  if (filters.minSalary > 0 || filters.maxSalary < 500000) {
    const min = filters.minSalary > 0 ? `$${filters.minSalary.toLocaleString()}` : 'Any';
    const max = filters.maxSalary < 500000 ? `$${filters.maxSalary.toLocaleString()}` : 'Any';
    activeFilters.push(`Salary: ${min} - ${max}`);
  }

  if (filters.industries.length > 0) {
    activeFilters.push(`Industries: ${filters.industries.join(', ')}`);
  }

  const quickFilters = Object.entries(filters.quickFilters)
    .filter(([_, active]) => active)
    .map(([key, _]) => key.charAt(0).toUpperCase() + key.slice(1));

  if (quickFilters.length > 0) {
    activeFilters.push(...quickFilters);
  }

  return activeFilters.length > 0 ? activeFilters.join(' • ') : 'No filters applied';
};

/**
 * Count active filters
 */
export const countActiveFilters = (filters: JobFilters): number => {
  let count = 0;

  if (filters.location) count++;
  if (filters.remoteOnly) count++;
  if (filters.inPersonOnly) count++;
  if (filters.employmentTypes.length > 0) count++;
  if (filters.jobCategories.length > 0) count++;
  if (filters.minSalary > 0 || filters.maxSalary < 500000) count++;
  if (filters.industries.length > 0) count++;
  if (filters.hasLogo) count++;

  count += Object.values(filters.quickFilters).filter(Boolean).length;

  return count;
};
