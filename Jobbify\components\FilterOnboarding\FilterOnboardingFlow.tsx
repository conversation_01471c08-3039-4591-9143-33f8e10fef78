import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  SafeAreaView,
  StatusBar
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { FontAwesome } from '@expo/vector-icons';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
// Removed complex animations to prevent crashes

// Import individual cards
import WelcomeCard from './cards/WelcomeCard';
import LocationCard from './cards/LocationCard';
import WorkStyleCard from './cards/WorkStyleCard';
import JobTypeCard from './cards/JobTypeCard';
import ExperienceLevelCard from './cards/ExperienceLevelCard';
import SalaryRangeCard from './cards/SalaryRangeCard';
import JobCategoriesCard from './cards/JobCategoriesCard';
import CompletionCard from './cards/CompletionCard';

// Removed screen width - not needed without animations

export interface FilterPreferences {
  location: string;
  workStyle: 'remote' | 'hybrid' | 'in-person' | '';
  jobTypes: string[];
  experienceLevel: string;
  salaryRange: { min: number; max: number };
  jobCategories: string[];
  completed: boolean;
}

interface FilterOnboardingFlowProps {
  onComplete: (preferences: FilterPreferences) => void;
  onSkip?: () => void;
}

const CARDS = [
  { id: 'welcome', title: 'Welcome', component: WelcomeCard },
  { id: 'location', title: 'Location', component: LocationCard },
  { id: 'workStyle', title: 'Work Style', component: WorkStyleCard },
  { id: 'jobType', title: 'Job Type', component: JobTypeCard },
  { id: 'experience', title: 'Experience', component: ExperienceLevelCard },
  { id: 'salary', title: 'Salary', component: SalaryRangeCard },
  { id: 'categories', title: 'Categories', component: JobCategoriesCard },
  { id: 'completion', title: 'Complete', component: CompletionCard },
];

export default function FilterOnboardingFlow({ onComplete, onSkip }: FilterOnboardingFlowProps) {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [preferences, setPreferences] = useState<FilterPreferences>({
    location: '',
    workStyle: '',
    jobTypes: [],
    experienceLevel: '',
    salaryRange: { min: 0, max: 200000 },
    jobCategories: [],
    completed: false
  });

  // Removed complex animations to prevent crashes

  const handleNext = (cardData?: any) => {
    try {
      console.log('[FILTER_ONBOARDING] Moving to next card, data:', cardData);

      if (cardData) {
        setPreferences(prev => ({ ...prev, ...cardData }));
      }

      if (currentCardIndex < CARDS.length - 1) {
        // Simplified navigation - remove complex animation that might cause crashes
        setCurrentCardIndex(prev => prev + 1);
      } else {
        // Complete the flow - clean the preferences object to avoid cyclical references
        const cleanPreferences: FilterPreferences = {
          location: (cardData?.location || preferences.location || 'Remote').toString(),
          workStyle: (cardData?.workStyle || preferences.workStyle || '').toString(),
          jobTypes: Array.isArray(cardData?.jobTypes) ? cardData.jobTypes :
                   Array.isArray(preferences.jobTypes) ? preferences.jobTypes : [],
          experienceLevel: (cardData?.experienceLevel || preferences.experienceLevel || '').toString(),
          salaryRange: {
            min: Number(cardData?.salaryRange?.min || preferences.salaryRange?.min || 0),
            max: Number(cardData?.salaryRange?.max || preferences.salaryRange?.max || 200000)
          },
          jobCategories: Array.isArray(cardData?.jobCategories) ? cardData.jobCategories :
                        Array.isArray(preferences.jobCategories) ? preferences.jobCategories : [],
          completed: true
        };

        console.log('[FILTER_ONBOARDING] Completing flow with clean preferences:', cleanPreferences);

        try {
          onComplete(cleanPreferences);
        } catch (completeError) {
          console.error('[FILTER_ONBOARDING] Error in onComplete callback:', completeError);
          // Still try to complete with basic preferences
          onComplete({
            location: 'Remote',
            workStyle: '',
            jobTypes: [],
            experienceLevel: '',
            salaryRange: { min: 0, max: 200000 },
            jobCategories: [],
            completed: true
          });
        }
      }
    } catch (error) {
      console.error('[FILTER_ONBOARDING] Error in handleNext:', error);
      // Try to continue anyway
      if (currentCardIndex < CARDS.length - 1) {
        setCurrentCardIndex(prev => prev + 1);
      } else {
        // Emergency completion
        onComplete({
          location: 'Remote',
          workStyle: '',
          jobTypes: [],
          experienceLevel: '',
          salaryRange: { min: 0, max: 200000 },
          jobCategories: [],
          completed: true
        });
      }
    }
  };

  const handlePrevious = () => {
    if (currentCardIndex > 0) {
      // Simplified navigation - remove complex animation
      setCurrentCardIndex(prev => prev - 1);
    }
  };

  const handleSkip = () => {
    if (onSkip) {
      onSkip();
    }
  };

  const CurrentCard = CARDS[currentCardIndex].component;

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
      <StatusBar barStyle={theme === 'dark' ? 'light-content' : 'dark-content'} />
      
      {/* Header with Progress */}
      <View style={styles.header}>
        <View style={styles.progressContainer}>
        <View style={[styles.progressTrack, { backgroundColor: themeColors.border }]}>
        <View 
        style={[
        styles.progressBar, 
        { 
          backgroundColor: themeColors.tint,
            width: `${((currentCardIndex + 1) / CARDS.length) * 100}%`
            }
            ]} 
              />
            </View>
          <Text style={[styles.progressText, { color: themeColors.textSecondary }]}>
            {currentCardIndex + 1} of {CARDS.length}
          </Text>
        </View>
        
        {currentCardIndex === 0 && onSkip && (
          <TouchableOpacity onPress={handleSkip} style={styles.skipButton}>
            <Text style={[styles.skipText, { color: themeColors.textSecondary }]}>Skip</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Card Container */}
      <View style={styles.cardContainer}>
        <View style={styles.card}>
          <CurrentCard
            preferences={preferences}
            onNext={handleNext}
            onPrevious={currentCardIndex > 0 ? handlePrevious : undefined}
            theme={theme}
            themeColors={themeColors}
          />
        </View>
      </View>

      {/* Navigation Dots */}
      <View style={styles.dotsContainer}>
        {CARDS.map((_, index) => (
          <View
            key={index}
            style={[
              styles.dot,
              {
                backgroundColor: index <= currentCardIndex 
                  ? themeColors.tint 
                  : themeColors.border
              }
            ]}
          />
        ))}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 24,
  },
  progressContainer: {
    flex: 1,
    marginRight: 16,
  },
  progressTrack: {
    height: 4,
    borderRadius: 2,
    marginBottom: 8,
  },
  progressBar: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '500',
  },
  skipButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  skipText: {
    fontSize: 16,
    fontWeight: '500',
  },
  cardContainer: {
    flex: 1,
    paddingHorizontal: 24,
  },
  card: {
    flex: 1,
  },
  dotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 32,
    gap: 8,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
});
