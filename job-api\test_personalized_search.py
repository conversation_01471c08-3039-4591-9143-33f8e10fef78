#!/usr/bin/env python3
"""
Test script for the new personalized job search system
"""

import asyncio
import json
from datetime import datetime

# Test data for user preferences
TEST_USER_PREFERENCES = {
    "user_id": "test-user-123",
    "preferred_locations": ["San Francisco, CA", "Remote"],
    "preferred_job_types": ["Full-time", "Remote"],
    "preferred_industries": ["Technology", "Software"],
    "experience_level": "mid",
    "min_salary": 80000,
    "max_salary": 150000,
    "remote_work_preference": "preferred"
}

def test_cache_key_generation():
    """Test cache key generation"""
    from personalized_search import generate_cache_key
    
    cache_key = generate_cache_key(TEST_USER_PREFERENCES)
    print(f"✅ Cache key generated: {cache_key}")
    
    expected_parts = ["san_francisco_ca_remote", "full-time_remote", "technology_software", "mid"]
    print(f"Expected parts in key: {expected_parts}")
    
    return cache_key

def test_location_detection():
    """Test US location detection"""
    from personalized_search import is_us_location, is_remote_preferred
    
    test_locations = [
        "San Francisco, CA",
        "New York, NY", 
        "London, UK",
        "Remote",
        "Berlin, Germany"
    ]
    
    print("\n🌍 Location Detection Tests:")
    for location in test_locations:
        is_us = is_us_location(location)
        print(f"  {location}: {'US' if is_us else 'International'}")
    
    remote_pref = is_remote_preferred(TEST_USER_PREFERENCES)
    print(f"\n🏠 Remote preference: {remote_pref}")

def test_industry_mapping():
    """Test industry to API mapping"""
    from personalized_search import INDUSTRY_API_MAPPING
    
    print("\n🏭 Industry API Mapping:")
    for industry, mapping in INDUSTRY_API_MAPPING.items():
        print(f"  {industry}:")
        print(f"    Muse: {mapping.get('muse_category')}")
        print(f"    Jooble: {mapping.get('jooble_keywords')}")
        print(f"    Adzuna: {mapping.get('adzuna_keywords')}")

async def test_api_calls():
    """Test API calling functionality"""
    print("\n🔄 Testing API Calls:")
    
    try:
        # Import the API functions
        from jooble_service import jooble
        from muse_service import muse
        from job_service import adzuna, arbeitnow
        import httpx
        
        async with httpx.AsyncClient() as client:
            print("  Testing Jooble API...")
            try:
                jooble_jobs = await jooble(client, "software engineer", "San Francisco, CA")
                print(f"    ✅ Jooble: {len(jooble_jobs)} jobs")
            except Exception as e:
                print(f"    ❌ Jooble error: {e}")
            
            print("  Testing Muse API...")
            try:
                muse_jobs = await muse(client, "Computer and IT", "San Francisco, CA")
                print(f"    ✅ Muse: {len(muse_jobs)} jobs")
            except Exception as e:
                print(f"    ❌ Muse error: {e}")
            
            print("  Testing Adzuna API...")
            try:
                adzuna_jobs = await adzuna(client)
                print(f"    ✅ Adzuna: {len(adzuna_jobs)} jobs")
            except Exception as e:
                print(f"    ❌ Adzuna error: {e}")
            
            print("  Testing Arbeitnow API...")
            try:
                arbeitnow_jobs = await arbeitnow(client)
                print(f"    ✅ Arbeitnow: {len(arbeitnow_jobs)} jobs")
            except Exception as e:
                print(f"    ❌ Arbeitnow error: {e}")
                
    except ImportError as e:
        print(f"    ❌ Import error: {e}")

def test_job_filtering():
    """Test job filtering and ranking"""
    print("\n🔍 Testing Job Filtering:")
    
    # Mock job data
    mock_jobs = [
        {
            "title": "Senior Software Engineer",
            "company": "Tech Corp",
            "location": "San Francisco, CA",
            "salary": "$120,000",
            "description": "Python, React, AWS",
            "api_source": "jooble"
        },
        {
            "title": "Frontend Developer",
            "company": "Startup Inc",
            "location": "Remote",
            "salary": "$90,000",
            "description": "JavaScript, React, Node.js",
            "api_source": "muse"
        },
        {
            "title": "Data Scientist",
            "company": "Big Data Co",
            "location": "New York, NY",
            "salary": "$110,000",
            "description": "Python, Machine Learning, SQL",
            "api_source": "adzuna"
        }
    ]
    
    try:
        from personalized_search import filter_and_rank_jobs, remove_duplicates
        
        # Test filtering
        filtered_jobs = filter_and_rank_jobs(mock_jobs, TEST_USER_PREFERENCES)
        print(f"  Filtered {len(mock_jobs)} jobs to {len(filtered_jobs)} jobs")
        
        # Test deduplication
        unique_jobs = remove_duplicates(filtered_jobs)
        print(f"  After deduplication: {len(unique_jobs)} jobs")
        
        # Show top job with score
        if unique_jobs:
            top_job = unique_jobs[0]
            print(f"  Top job: {top_job.get('title')} at {top_job.get('company')} (Score: {top_job.get('relevance_score', 0)})")
            
    except ImportError as e:
        print(f"    ❌ Import error: {e}")

def main():
    """Run all tests"""
    print("🧪 Testing Personalized Job Search System")
    print("=" * 50)
    
    # Test 1: Cache key generation
    print("\n1️⃣ Testing Cache Key Generation:")
    cache_key = test_cache_key_generation()
    
    # Test 2: Location detection
    test_location_detection()
    
    # Test 3: Industry mapping
    test_industry_mapping()
    
    # Test 4: Job filtering
    test_job_filtering()
    
    # Test 5: API calls (async)
    print("\n5️⃣ Testing API Calls:")
    try:
        asyncio.run(test_api_calls())
    except Exception as e:
        print(f"❌ Async test error: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Test suite completed!")
    print("\nNext steps:")
    print("1. Start the FastAPI server: python -m uvicorn main:app --reload")
    print("2. Test the endpoint: POST /api/jobs/personalized-search")
    print("3. Update your React Native app to use fetchJobsForUser()")

if __name__ == "__main__":
    main()
