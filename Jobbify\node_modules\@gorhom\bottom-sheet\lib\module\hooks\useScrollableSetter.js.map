{"version": 3, "names": ["useCallback", "useEffect", "findNodeHandle", "useBottomSheetInternal", "useScrollableSetter", "ref", "type", "contentOffsetY", "refreshable", "useFocusHook", "animatedScrollableType", "animatedScrollableContentOffsetY", "rootScrollableContentOffsetY", "isContentHeightFixed", "isScrollableRefreshable", "setScrollableRef", "removeScrollableRef", "handleSettingScrollable", "value", "id", "current", "node", "console", "warn"], "sourceRoot": "../../../src", "sources": ["hooks/useScrollableSetter.ts"], "mappings": ";;AACA,SAASA,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAI9C,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,sBAAsB,QAAQ,0BAA0B;AAEjE,OAAO,MAAMC,mBAAmB,GAAGA,CACjCC,GAAgC,EAChCC,IAAqB,EACrBC,cAAmC,EACnCC,WAAoB,EACpBC,YAAY,GAAGR,SAAS,KACrB;EACH;EACA,MAAM;IACJS,sBAAsB;IACtBC,gCAAgC,EAAEC,4BAA4B;IAC9DC,oBAAoB;IACpBC,uBAAuB;IACvBC,gBAAgB;IAChBC;EACF,CAAC,GAAGb,sBAAsB,CAAC,CAAC;;EAE5B;EACA,MAAMc,uBAAuB,GAAGjB,WAAW,CAAC,MAAM;IAChD;IACAY,4BAA4B,CAACM,KAAK,GAAGX,cAAc,CAACW,KAAK;IACzDR,sBAAsB,CAACQ,KAAK,GAAGZ,IAAI;IACnCQ,uBAAuB,CAACI,KAAK,GAAGV,WAAW;IAC3CK,oBAAoB,CAACK,KAAK,GAAG,KAAK;;IAElC;IACA,MAAMC,EAAE,GAAGjB,cAAc,CAACG,GAAG,CAACe,OAAO,CAAC;IACtC,IAAID,EAAE,EAAE;MACNJ,gBAAgB,CAAC;QACfI,EAAE,EAAEA,EAAE;QACNE,IAAI,EAAEhB;MACR,CAAC,CAAC;IACJ,CAAC,MAAM;MACLiB,OAAO,CAACC,IAAI,CAAC,8CAA8C,CAAC;IAC9D;IAEA,OAAO,MAAM;MACXP,mBAAmB,CAACX,GAAG,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CACDA,GAAG,EACHC,IAAI,EACJE,WAAW,EACXE,sBAAsB,EACtBE,4BAA4B,EAC5BL,cAAc,EACdO,uBAAuB,EACvBD,oBAAoB,EACpBE,gBAAgB,EAChBC,mBAAmB,CACpB,CAAC;;EAEF;EACAP,YAAY,CAACQ,uBAAuB,CAAC;AACvC,CAAC", "ignoreList": []}