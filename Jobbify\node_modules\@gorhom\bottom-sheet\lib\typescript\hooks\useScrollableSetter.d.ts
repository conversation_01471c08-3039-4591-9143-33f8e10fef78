import type React from 'react';
import type { SharedValue } from 'react-native-reanimated';
import type { SCROLLABLE_TYPE } from '../constants';
import type { Scrollable } from '../types';
export declare const useScrollableSetter: (ref: React.RefObject<Scrollable>, type: SCROLLABLE_TYPE, contentOffsetY: SharedValue<number>, refreshable: boolean, useFocusHook?: typeof React.useEffect) => void;
//# sourceMappingURL=useScrollableSetter.d.ts.map