"""
Prometheus metrics collection for job search API
Tracks performance, cache efficiency, and API health
"""

from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, generate_latest
import time
from contextlib import contextmanager
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

# Create custom registry to avoid conflicts
registry = CollectorRegistry()

# Cache metrics
cache_hits = Counter(
    "cache_hits_total", 
    "Total cache hits", 
    ["source"], 
    registry=registry
)

cache_misses = Counter(
    "cache_misses_total", 
    "Total cache misses", 
    ["source"], 
    registry=registry
)

# API call metrics
api_calls_total = Counter(
    "api_calls_total", 
    "External API calls", 
    ["api", "status"], 
    registry=registry
)

api_errors_total = Counter(
    "api_errors_total", 
    "External API errors", 
    ["api", "error_type"], 
    registry=registry
)

# Response time metrics with sensible buckets
response_time_ms = Histogram(
    "response_time_ms", 
    "Endpoint response time in milliseconds",
    buckets=[50, 100, 250, 500, 1000, 2500, 5000, 10000],
    registry=registry
)

# Job quality metrics
job_quality_score = Histogram(
    "job_quality_score",
    "Distribution of job quality scores",
    buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
    registry=registry
)

# User activity metrics
user_searches_total = Counter(
    "user_searches_total",
    "Total user searches",
    ["user_plan", "search_type"],
    registry=registry
)

# System health metrics
active_connections = Gauge(
    "active_connections",
    "Number of active database connections",
    registry=registry
)

redis_connection_status = Gauge(
    "redis_connection_status",
    "Redis connection status (1=connected, 0=disconnected)",
    registry=registry
)

# API quota metrics
api_quota_remaining = Gauge(
    "api_quota_remaining",
    "Remaining API quota",
    ["api"],
    registry=registry
)

api_quota_usage_percent = Gauge(
    "api_quota_usage_percent",
    "API quota usage percentage",
    ["api"],
    registry=registry
)

def exposition() -> bytes:
    """Generate Prometheus exposition format"""
    try:
        return generate_latest(registry)
    except Exception as e:
        logger.error(f"Error generating metrics: {e}")
        return b"# Error generating metrics\n"

@contextmanager
def track_response_time(endpoint: str = "unknown"):
    """Context manager to track endpoint response time"""
    start_time = time.time()
    try:
        yield
    finally:
        duration_ms = (time.time() - start_time) * 1000
        response_time_ms.observe(duration_ms)
        logger.debug(f"Endpoint {endpoint} took {duration_ms:.2f}ms")

def record_cache_hit(source: str = "redis"):
    """Record a cache hit"""
    cache_hits.labels(source=source).inc()
    logger.debug(f"Cache hit recorded for source: {source}")

def record_cache_miss(source: str = "redis"):
    """Record a cache miss"""
    cache_misses.labels(source=source).inc()
    logger.debug(f"Cache miss recorded for source: {source}")

def record_api_call(api_name: str, status: str = "success"):
    """Record an API call"""
    api_calls_total.labels(api=api_name, status=status).inc()
    logger.debug(f"API call recorded: {api_name} - {status}")

def record_api_error(api_name: str, error_type: str = "unknown"):
    """Record an API error"""
    api_errors_total.labels(api=api_name, error_type=error_type).inc()
    logger.warning(f"API error recorded: {api_name} - {error_type}")

def record_job_quality(score: float):
    """Record job quality score distribution"""
    job_quality_score.observe(score)

def record_user_search(user_plan: str = "free", search_type: str = "personalized"):
    """Record user search activity"""
    user_searches_total.labels(user_plan=user_plan, search_type=search_type).inc()

def update_redis_status(connected: bool):
    """Update Redis connection status"""
    redis_connection_status.set(1 if connected else 0)

def update_api_quota(api_name: str, remaining: int, total: int):
    """Update API quota metrics"""
    api_quota_remaining.labels(api=api_name).set(remaining)
    usage_percent = ((total - remaining) / total * 100) if total > 0 else 0
    api_quota_usage_percent.labels(api=api_name).set(usage_percent)

def get_metrics_summary() -> Dict[str, Any]:
    """Get a summary of current metrics for debugging"""
    try:
        # This is a simplified summary - in production you'd parse the exposition format
        return {
            "cache_metrics": "Available via /metrics endpoint",
            "api_metrics": "Available via /metrics endpoint", 
            "response_time_metrics": "Available via /metrics endpoint",
            "registry_collectors": len(registry._collector_to_names),
            "status": "healthy"
        }
    except Exception as e:
        logger.error(f"Error getting metrics summary: {e}")
        return {"status": "error", "error": str(e)}

# Decorator for automatic response time tracking
def track_endpoint_performance(endpoint_name: str):
    """Decorator to automatically track endpoint performance"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            with track_response_time(endpoint_name):
                return await func(*args, **kwargs)
        return wrapper
    return decorator

# Initialize metrics on import
def initialize_metrics():
    """Initialize default metric values"""
    try:
        # Set initial Redis status (will be updated by cache module)
        redis_connection_status.set(0)
        
        # Initialize API quota gauges
        apis = ["jooble", "adzuna", "muse", "arbeitnow", "remoteok"]
        for api in apis:
            api_quota_remaining.labels(api=api).set(0)
            api_quota_usage_percent.labels(api=api).set(0)
        
        logger.info("Metrics initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing metrics: {e}")

# Initialize on module import
initialize_metrics()
