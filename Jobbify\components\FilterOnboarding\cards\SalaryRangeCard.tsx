import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';
import Slider from '@react-native-community/slider';

interface SalaryRangeCardProps {
  preferences: any;
  onNext: (data: any) => void;
  onPrevious?: () => void;
  theme: string;
  themeColors: any;
}

const { width } = Dimensions.get('window');

const PRESET_RANGES = [
  { label: 'Entry Level', min: 40000, max: 60000 },
  { label: 'Mid Level', min: 60000, max: 100000 },
  { label: 'Senior Level', min: 100000, max: 150000 },
  { label: 'Executive', min: 150000, max: 250000 },
];

export default function SalaryRangeCard({ 
  preferences, 
  onNext, 
  onPrevious, 
  theme, 
  themeColors 
}: SalaryRangeCardProps) {
  const [salaryRange, setSalaryRange] = useState({
    min: preferences.salaryRange?.min || 50000,
    max: preferences.salaryRange?.max || 120000
  });

  const formatSalary = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const handlePresetSelect = (preset: typeof PRESET_RANGES[0]) => {
    setSalaryRange({ min: preset.min, max: preset.max });
  };

  const handleNext = () => {
    onNext({ salaryRange });
  };

  const renderPresetOption = (preset: typeof PRESET_RANGES[0], index: number) => {
    const isSelected = salaryRange.min === preset.min && salaryRange.max === preset.max;
    
    return (
      <TouchableOpacity
        key={index}
        style={[
          styles.presetCard,
          {
            backgroundColor: isSelected ? themeColors.tint : themeColors.card,
            borderColor: isSelected ? themeColors.tint : themeColors.border,
          }
        ]}
        onPress={() => handlePresetSelect(preset)}
      >
        <Text style={[
          styles.presetLabel,
          { color: isSelected ? '#FFFFFF' : themeColors.text }
        ]}>
          {preset.label}
        </Text>
        <Text style={[
          styles.presetRange,
          { color: isSelected ? 'rgba(255,255,255,0.8)' : themeColors.textSecondary }
        ]}>
          {formatSalary(preset.min)} - {formatSalary(preset.max)}
        </Text>
        {isSelected && (
          <FontAwesome name="check" size={16} color="#FFFFFF" style={styles.checkIcon} />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <Animated.View entering={FadeInUp.delay(200)} style={styles.header}>
        <Text style={[styles.title, { color: themeColors.text }]}>
          What's your expected salary range?
        </Text>
        <Text style={[styles.subtitle, { color: themeColors.textSecondary }]}>
          This helps us show relevant opportunities
        </Text>
      </Animated.View>

      <Animated.View entering={FadeInUp.delay(400)} style={styles.content}>
        {/* Current Range Display */}
        <View style={[styles.rangeDisplay, { backgroundColor: themeColors.card }]}>
          <View style={styles.rangeHeader}>
            <FontAwesome name="dollar" size={20} color={themeColors.tint} />
            <Text style={[styles.rangeTitle, { color: themeColors.text }]}>
              Selected Range
            </Text>
          </View>
          <Text style={[styles.currentRange, { color: themeColors.tint }]}>
            {formatSalary(salaryRange.min)} - {formatSalary(salaryRange.max)}
          </Text>
        </View>

        {/* Quick Presets */}
        <View style={styles.presetsSection}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
            Quick Select
          </Text>
          <View style={styles.presetsGrid}>
            {PRESET_RANGES.map(renderPresetOption)}
          </View>
        </View>

        {/* Custom Sliders */}
        <View style={styles.slidersSection}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
            Custom Range
          </Text>
          
          <View style={styles.sliderContainer}>
            <Text style={[styles.sliderLabel, { color: themeColors.textSecondary }]}>
              Minimum: {formatSalary(salaryRange.min)}
            </Text>
            <Slider
              style={styles.slider}
              minimumValue={20000}
              maximumValue={300000}
              step={5000}
              value={salaryRange.min}
              onValueChange={(value) => setSalaryRange(prev => ({ 
                ...prev, 
                min: Math.min(value, prev.max - 5000) 
              }))}
              minimumTrackTintColor={themeColors.tint}
              maximumTrackTintColor={themeColors.border}
              thumbStyle={{ backgroundColor: themeColors.tint }}
            />
          </View>

          <View style={styles.sliderContainer}>
            <Text style={[styles.sliderLabel, { color: themeColors.textSecondary }]}>
              Maximum: {formatSalary(salaryRange.max)}
            </Text>
            <Slider
              style={styles.slider}
              minimumValue={30000}
              maximumValue={500000}
              step={5000}
              value={salaryRange.max}
              onValueChange={(value) => setSalaryRange(prev => ({ 
                ...prev, 
                max: Math.max(value, prev.min + 5000) 
              }))}
              minimumTrackTintColor={themeColors.tint}
              maximumTrackTintColor={themeColors.border}
              thumbStyle={{ backgroundColor: themeColors.tint }}
            />
          </View>
        </View>
      </Animated.View>

      <Animated.View entering={FadeInDown.delay(600)} style={styles.buttonContainer}>
        <View style={styles.buttonRow}>
          {onPrevious && (
            <TouchableOpacity
              style={[styles.backButton, { borderColor: themeColors.border }]}
              onPress={onPrevious}
            >
              <FontAwesome name="arrow-left" size={16} color={themeColors.textSecondary} />
              <Text style={[styles.backText, { color: themeColors.textSecondary }]}>
                Back
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[
              styles.nextButton,
              { backgroundColor: themeColors.tint },
              !onPrevious && styles.fullWidthButton
            ]}
            onPress={handleNext}
          >
            <Text style={styles.nextText}>Continue</Text>
            <FontAwesome name="arrow-right" size={16} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
    paddingTop: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  content: {
    flex: 1,
  },
  rangeDisplay: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
    alignItems: 'center',
  },
  rangeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  rangeTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  currentRange: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  presetsSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  presetsGrid: {
    gap: 12,
  },
  presetCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 2,
  },
  presetLabel: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  presetRange: {
    fontSize: 14,
    marginRight: 8,
  },
  checkIcon: {
    marginLeft: 8,
  },
  slidersSection: {
    flex: 1,
  },
  sliderContainer: {
    marginBottom: 24,
  },
  sliderLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  buttonContainer: {
    paddingBottom: 32,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 2,
    gap: 8,
    flex: 1,
  },
  backText: {
    fontSize: 16,
    fontWeight: '600',
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
    flex: 2,
  },
  fullWidthButton: {
    flex: 1,
  },
  nextText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
