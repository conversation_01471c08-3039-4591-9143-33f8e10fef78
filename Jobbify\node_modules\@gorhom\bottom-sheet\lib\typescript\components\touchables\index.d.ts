import type { TouchableHighlight as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>igh<PERSON>, TouchableOpacity as <PERSON>NTou<PERSON><PERSON><PERSON><PERSON>city, TouchableWithoutFeedback as RNTouchableWithoutFeedback } from 'react-native';
declare const _default: {
    TouchableOpacity: typeof RNTouchableOpacity;
    TouchableHighlight: typeof RNTouchableHighlight;
    TouchableWithoutFeedback: typeof RNTouchableWithoutFeedback;
};
export default _default;
//# sourceMappingURL=index.d.ts.map