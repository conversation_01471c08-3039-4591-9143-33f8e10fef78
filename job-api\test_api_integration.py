#!/usr/bin/env python3
import asyncio
import httpx
from jooble_service import jooble, map_jooble_job
from muse_service import muse, map_muse_job

async def test_new_apis():
    """Test the new API integrations directly"""
    print("🔧 Testing new API integrations...")
    
    async with httpx.AsyncClient() as client:
        print("\n1. Testing Jooble API...")
        try:
            jooble_jobs = await jooble(client, "developer", "Remote")
            print(f"   ✅ <PERSON><PERSON><PERSON> returned {len(jooble_jobs)} jobs")
            if jooble_jobs:
                sample_job = jooble_jobs[0]
                print(f"   Sample job: {sample_job.get('title', 'No title')} at {sample_job.get('company', 'No company')}")
                mapped_job = map_jooble_job(sample_job)
                print(f"   Mapped job title: {mapped_job.get('title', 'No title')}")
        except Exception as e:
            print(f"   ❌ Jooble Error: {e}")
        
        print("\n2. Testing The Muse API...")
        try:
            muse_jobs = await muse(client, "Computer and IT", "Flexible / Remote")
            print(f"   ✅ The Muse returned {len(muse_jobs)} jobs")
            if muse_jobs:
                sample_job = muse_jobs[0]
                print(f"   Sample job: {sample_job.get('name', 'No title')} at {sample_job.get('company', {}).get('name', 'No company')}")
                mapped_job = map_muse_job(sample_job)
                print(f"   Mapped job title: {mapped_job.get('title', 'No title')}")
        except Exception as e:
            print(f"   ❌ The Muse Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_new_apis())
