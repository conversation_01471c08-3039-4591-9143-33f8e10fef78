{"version": 3, "names": ["_react", "require", "_utilities", "_useBottomSheetInternal", "useScrollableSetter", "ref", "type", "contentOffsetY", "refreshable", "useFocusHook", "useEffect", "animatedScrollableType", "animatedScrollableContentOffsetY", "rootScrollableContentOffsetY", "isContentHeightFixed", "isScrollableRefreshable", "setScrollableRef", "removeScrollableRef", "useBottomSheetInternal", "handleSettingScrollable", "useCallback", "value", "id", "findNodeHandle", "current", "node", "console", "warn", "exports"], "sourceRoot": "../../../src", "sources": ["hooks/useScrollableSetter.ts"], "mappings": ";;;;;;AACA,IAAAA,MAAA,GAAAC,OAAA;AAIA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,uBAAA,GAAAF,OAAA;AAEO,MAAMG,mBAAmB,GAAGA,CACjCC,GAAgC,EAChCC,IAAqB,EACrBC,cAAmC,EACnCC,WAAoB,EACpBC,YAAY,GAAGC,gBAAS,KACrB;EACH;EACA,MAAM;IACJC,sBAAsB;IACtBC,gCAAgC,EAAEC,4BAA4B;IAC9DC,oBAAoB;IACpBC,uBAAuB;IACvBC,gBAAgB;IAChBC;EACF,CAAC,GAAG,IAAAC,8CAAsB,EAAC,CAAC;;EAE5B;EACA,MAAMC,uBAAuB,GAAG,IAAAC,kBAAW,EAAC,MAAM;IAChD;IACAP,4BAA4B,CAACQ,KAAK,GAAGd,cAAc,CAACc,KAAK;IACzDV,sBAAsB,CAACU,KAAK,GAAGf,IAAI;IACnCS,uBAAuB,CAACM,KAAK,GAAGb,WAAW;IAC3CM,oBAAoB,CAACO,KAAK,GAAG,KAAK;;IAElC;IACA,MAAMC,EAAE,GAAG,IAAAC,yBAAc,EAAClB,GAAG,CAACmB,OAAO,CAAC;IACtC,IAAIF,EAAE,EAAE;MACNN,gBAAgB,CAAC;QACfM,EAAE,EAAEA,EAAE;QACNG,IAAI,EAAEpB;MACR,CAAC,CAAC;IACJ,CAAC,MAAM;MACLqB,OAAO,CAACC,IAAI,CAAC,8CAA8C,CAAC;IAC9D;IAEA,OAAO,MAAM;MACXV,mBAAmB,CAACZ,GAAG,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CACDA,GAAG,EACHC,IAAI,EACJE,WAAW,EACXG,sBAAsB,EACtBE,4BAA4B,EAC5BN,cAAc,EACdQ,uBAAuB,EACvBD,oBAAoB,EACpBE,gBAAgB,EAChBC,mBAAmB,CACpB,CAAC;;EAEF;EACAR,YAAY,CAACU,uBAAuB,CAAC;AACvC,CAAC;AAACS,OAAA,CAAAxB,mBAAA,GAAAA,mBAAA", "ignoreList": []}