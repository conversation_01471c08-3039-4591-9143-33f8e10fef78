#!/usr/bin/env python3
from job_service import supabase

def check_database():
    """Check jobs directly from the database"""
    print("🔍 Checking jobs directly from database...")
    
    try:
        # Get all jobs from database
        response = supabase.table("jobs").select("*").limit(500).execute()
        jobs = response.data
        
        print(f"Total jobs in database: {len(jobs)}")
        
        # Count by source field
        source_counts = {}
        sample_jobs = {}
        
        for job in jobs:
            # Check the 'source' field directly
            source = job.get('source') or 'unknown'
            source_counts[source] = source_counts.get(source, 0) + 1
            
            # Collect sample jobs from each source
            if source not in sample_jobs:
                sample_jobs[source] = []
            if len(sample_jobs[source]) < 2:
                sample_jobs[source].append(job)
        
        print("\nJobs by source field:")
        for source, count in sorted(source_counts.items()):
            print(f"  {source}: {count} jobs")
            
        print("\nSample jobs from each source:")
        for source, jobs_list in sample_jobs.items():
            print(f"\n{(source or 'unknown').upper()} jobs:")
            for i, job in enumerate(jobs_list):
                print(f"  {i+1}. {job.get('title', 'No title')} at {job.get('company', 'No company')}")
                print(f"     External ID: {job.get('external_id', 'None')}")
                print(f"     Source: {job.get('source', 'None')}")
                
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_database()
