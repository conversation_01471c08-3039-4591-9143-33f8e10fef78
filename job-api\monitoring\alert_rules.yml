groups:
  - name: job_api_alerts
    rules:
      # Alert #1: High API Error Rate
      - alert: HighAPIErrorRate
        expr: sum(rate(api_errors_total{api="jooble"}[15m])) > 20
        for: 2m
        labels:
          severity: critical
          service: job-api
          api: jooble
        annotations:
          summary: "High error rate detected for Jooble API"
          description: "Jooble API has recorded {{ $value }} errors in the last 15 minutes, which exceeds the threshold of 20."
          runbook_url: "https://docs.example.com/runbooks/api-errors"

      - alert: HighAPIErrorRateGeneral
        expr: sum(rate(api_errors_total[15m])) by (api) > 20
        for: 2m
        labels:
          severity: warning
          service: job-api
        annotations:
          summary: "High error rate detected for {{ $labels.api }} API"
          description: "{{ $labels.api }} API has recorded {{ $value }} errors in the last 15 minutes."

      # Alert #2: Low Cache Hit Ratio
      - alert: LowCacheHitRatio
        expr: |
          (
            rate(cache_hits_total[1h]) / 
            (rate(cache_hits_total[1h]) + rate(cache_misses_total[1h]))
          ) < 0.5
        for: 60m
        labels:
          severity: warning
          service: job-api
          component: cache
        annotations:
          summary: "Cache hit ratio is below 50%"
          description: "Cache hit ratio has been {{ $value | humanizePercentage }} for more than 1 hour, indicating poor cache performance."
          runbook_url: "https://docs.example.com/runbooks/cache-performance"

      # Alert #3: Redis Connection Down
      - alert: RedisConnectionDown
        expr: redis_connection_status == 0
        for: 5m
        labels:
          severity: critical
          service: job-api
          component: redis
        annotations:
          summary: "Redis connection is down"
          description: "Redis connection has been down for more than 5 minutes."

      # Alert #4: High Response Time
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(response_time_ms_bucket[5m])) > 5000
        for: 10m
        labels:
          severity: warning
          service: job-api
          component: api
        annotations:
          summary: "High API response time"
          description: "95th percentile response time is {{ $value }}ms, exceeding 5 second threshold."

      # Alert #5: API Quota Exhaustion
      - alert: APIQuotaExhausted
        expr: api_quota_remaining < 100
        for: 1m
        labels:
          severity: critical
          service: job-api
          component: api-quota
        annotations:
          summary: "API quota nearly exhausted for {{ $labels.api }}"
          description: "{{ $labels.api }} API has only {{ $value }} requests remaining in quota."

      # Alert #6: High User Search Volume
      - alert: HighUserSearchVolume
        expr: sum(rate(user_searches_total[5m])) > 10
        for: 15m
        labels:
          severity: info
          service: job-api
          component: usage
        annotations:
          summary: "High user search volume detected"
          description: "User search rate is {{ $value }} searches per second over the last 5 minutes."

      # Alert #7: Job Quality Score Drop
      - alert: LowJobQualityScores
        expr: histogram_quantile(0.5, rate(job_quality_score_bucket[30m])) < 0.3
        for: 30m
        labels:
          severity: warning
          service: job-api
          component: quality
        annotations:
          summary: "Job quality scores are consistently low"
          description: "Median job quality score has been {{ $value }} for 30 minutes, indicating poor job data quality."

  - name: system_alerts
    rules:
      # System health alerts
      - alert: ServiceDown
        expr: up == 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} has been down for more than 5 minutes."

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) > 0.9
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 90% for more than 10 minutes."
