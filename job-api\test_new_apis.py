#!/usr/bin/env python3
import requests

def test_api():
    base_url = "http://localhost:8000"
    
    print("🔧 Testing API endpoints...")
    
    # Test health endpoint
    print("\n1. Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test jobs refresh
    print("\n2. Testing jobs refresh...")
    try:
        response = requests.post(f"{base_url}/jobs/refresh")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test jobs list
    print("\n3. Testing jobs list...")
    try:
        response = requests.get(f"{base_url}/jobs/")
        print(f"   Status: {response.status_code}")
        jobs = response.json()
        print(f"   Number of jobs: {len(jobs)}")
        if jobs:
            print(f"   Sample job title: {jobs[0].get('title', 'No title')}")
            print(f"   Sample job company: {jobs[0].get('company', 'No company')}")
            print(f"   Sample job source: {jobs[0].get('description', '')[:100]}...")
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    test_api()
