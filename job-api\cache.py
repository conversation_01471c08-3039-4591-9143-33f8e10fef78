"""
Redis-backed cache layer for job search optimization
Reduces API costs and improves response times
"""

import redis.asyncio as redis
import json
import os
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class JobSearchCache:
    def __init__(self):
        self.redis_client = None
        self.redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379')
        self.connected = False
        
    async def connect(self):
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            
            # Test connection
            await self.redis_client.ping()
            self.connected = True
            logger.info(f"✅ Redis connected: {self.redis_url}")

            # Update Prometheus metrics
            try:
                from metrics import update_redis_status
                update_redis_status(True)
            except ImportError:
                pass

        except Exception as e:
            logger.warning(f"❌ Redis connection failed: {e}")
            logger.info("Application will continue without Redis caching")
            self.connected = False
            self.redis_client = None

            # Update Prometheus metrics
            try:
                from metrics import update_redis_status
                update_redis_status(False)
            except ImportError:
                pass
    
    async def disconnect(self):
        """Close Redis connection"""
        if self.redis_client:
            await self.redis_client.close()
            self.connected = False
            logger.info("Redis connection closed")
    
    def _generate_cache_key(self, location: str, job_types: list, experience: str, industries: list) -> str:
        """Generate standardized cache key"""
        # Sort lists for consistent keys
        sorted_job_types = sorted(job_types) if job_types else []
        sorted_industries = sorted(industries) if industries else []
        
        key = f"jobs:{location}:{','.join(sorted_job_types)}:{experience}:{','.join(sorted_industries)}"
        return key.lower().replace(' ', '_').replace(',', '_')
    
    def _determine_ttl(self, cache_key: str, user_count: int = 0) -> int:
        """Determine TTL based on search popularity"""
        # Top-20 combos get 6 hours, long-tail gets 2 hours
        if user_count >= 5:  # Popular search
            return 6 * 3600  # 6 hours
        else:  # Long-tail search
            return 2 * 3600  # 2 hours
    
    async def get(self, key: str) -> Optional[Dict[str, Any]]:
        """Get cached data by key"""
        if not self.connected or not self.redis_client:
            logger.warning("Redis not connected, cache miss")
            return None
        
        try:
            cached_data = await self.redis_client.get(key)
            if cached_data:
                data = json.loads(cached_data)
                logger.info(f"🎯 Cache HIT: {key}")

                # Update access metrics
                await self.incr(f"cache_hits:{datetime.now().strftime('%Y-%m-%d')}")

                # Record Prometheus metrics
                try:
                    from metrics import record_cache_hit
                    record_cache_hit("redis")
                except ImportError:
                    pass

                return data
            else:
                logger.info(f"❌ Cache MISS: {key}")
                await self.incr(f"cache_misses:{datetime.now().strftime('%Y-%m-%d')}")

                # Record Prometheus metrics
                try:
                    from metrics import record_cache_miss
                    record_cache_miss("redis")
                except ImportError:
                    pass

                return None
                
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            await self.incr(f"cache_errors:{datetime.now().strftime('%Y-%m-%d')}")
            return None
    
    async def set(self, key: str, data: Dict[str, Any], ttl: int) -> bool:
        """Set cached data with TTL"""
        if not self.connected or not self.redis_client:
            logger.warning("Redis not connected, skipping cache set")
            return False
        
        try:
            # Add metadata
            cache_payload = {
                "data": data,
                "cached_at": datetime.now().isoformat(),
                "expires_at": (datetime.now() + timedelta(seconds=ttl)).isoformat(),
                "ttl": ttl
            }
            
            await self.redis_client.setex(
                key, 
                ttl, 
                json.dumps(cache_payload, default=str)
            )
            
            logger.info(f"💾 Cache SET: {key} (TTL: {ttl}s)")
            await self.incr(f"cache_sets:{datetime.now().strftime('%Y-%m-%d')}")
            
            return True
            
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            await self.incr(f"cache_errors:{datetime.now().strftime('%Y-%m-%d')}")
            return False
    
    async def incr(self, key: str, amount: int = 1) -> int:
        """Increment counter (for API usage tracking)"""
        if not self.connected or not self.redis_client:
            return 0
        
        try:
            result = await self.redis_client.incr(key, amount)
            
            # Set expiry for daily counters
            if ":" in key and any(date_part in key for date_part in ["2024", "2025"]):
                await self.redis_client.expire(key, 86400 * 7)  # 7 days
            
            return result
            
        except Exception as e:
            logger.error(f"Cache incr error for key {key}: {e}")
            return 0
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics"""
        if not self.connected:
            return {"status": "disconnected"}
        
        try:
            today = datetime.now().strftime('%Y-%m-%d')
            
            hits = await self.redis_client.get(f"cache_hits:{today}") or 0
            misses = await self.redis_client.get(f"cache_misses:{today}") or 0
            sets = await self.redis_client.get(f"cache_sets:{today}") or 0
            errors = await self.redis_client.get(f"cache_errors:{today}") or 0
            
            total_requests = int(hits) + int(misses)
            hit_ratio = (int(hits) / total_requests * 100) if total_requests > 0 else 0
            
            # Get Redis info
            info = await self.redis_client.info()
            
            return {
                "status": "connected",
                "today": today,
                "cache_hits": int(hits),
                "cache_misses": int(misses),
                "cache_sets": int(sets),
                "cache_errors": int(errors),
                "hit_ratio": round(hit_ratio, 2),
                "total_requests": total_requests,
                "redis_memory": info.get("used_memory_human", "unknown"),
                "redis_keys": info.get("db0", {}).get("keys", 0) if "db0" in info else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {"status": "error", "error": str(e)}
    
    async def clear_expired(self) -> int:
        """Clear expired cache entries (manual cleanup)"""
        if not self.connected:
            return 0
        
        try:
            # Get all job cache keys
            keys = await self.redis_client.keys("jobs:*")
            expired_count = 0
            
            for key in keys:
                ttl = await self.redis_client.ttl(key)
                if ttl == -2:  # Key doesn't exist
                    expired_count += 1
                elif ttl == -1:  # Key exists but no expiry
                    # Set default expiry
                    await self.redis_client.expire(key, 2 * 3600)
            
            logger.info(f"🧹 Cleaned up {expired_count} expired cache entries")
            return expired_count
            
        except Exception as e:
            logger.error(f"Error clearing expired cache: {e}")
            return 0

# Global cache instance
cache = JobSearchCache()

# Helper functions for easy use
async def get_cached_jobs(location: str, job_types: list, experience: str, industries: list) -> Optional[Dict]:
    """Get cached job search results"""
    key = cache._generate_cache_key(location, job_types, experience, industries)
    return await cache.get(key)

async def cache_jobs(location: str, job_types: list, experience: str, industries: list, 
                    jobs_data: Dict, user_count: int = 0) -> bool:
    """Cache job search results"""
    key = cache._generate_cache_key(location, job_types, experience, industries)
    ttl = cache._determine_ttl(key, user_count)
    return await cache.set(key, jobs_data, ttl)

async def track_api_usage(api_name: str) -> int:
    """Track API usage for the day"""
    today = datetime.now().strftime('%Y-%m-%d')
    key = f"api_usage:{api_name}:{today}"
    return await cache.incr(key)

async def get_user_search_count(user_id: str) -> int:
    """Get user's search count for today"""
    today = datetime.now().strftime('%Y-%m-%d')
    key = f"usage:{user_id}:{today}"
    
    if not cache.connected:
        return 0
    
    try:
        count = await cache.redis_client.get(key) or 0
        return int(count)
    except:
        return 0

async def increment_user_searches(user_id: str) -> int:
    """Increment user's daily search count"""
    today = datetime.now().strftime('%Y-%m-%d')
    key = f"usage:{user_id}:{today}"
    return await cache.incr(key)

# Startup and shutdown handlers
async def init_cache():
    """Initialize cache connection"""
    await cache.connect()

async def close_cache():
    """Close cache connection"""
    await cache.disconnect()
