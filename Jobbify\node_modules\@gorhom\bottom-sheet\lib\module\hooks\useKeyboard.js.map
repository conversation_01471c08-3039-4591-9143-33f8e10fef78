{"version": 3, "names": ["useEffect", "Keyboard", "Platform", "runOnUI", "useAnimatedReaction", "useSharedValue", "useWorkletCallback", "KEYBOARD_STATE", "SCREEN_HEIGHT", "KEYBOARD_EVENT_MAPPER", "KEYBOARD_SHOW", "select", "ios", "android", "default", "KEYBOARD_HIDE", "useKeyboard", "shouldHandleKeyboardEvents", "keyboardState", "UNDETERMINED", "keyboardHeight", "keyboardAnimationEasing", "keyboardAnimationDuration", "temporaryCachedKeyboardEvent", "handleKeyboardEvent", "state", "height", "duration", "easing", "bottomOffset", "SHOWN", "value", "handleOnKeyboardShow", "event", "endCoordinates", "screenY", "handleOnKeyboardHide", "HIDDEN", "showSubscription", "addListener", "hideSubscription", "remove", "result", "params", "length", "animationEasing", "animationDuration"], "sourceRoot": "../../../src", "sources": ["hooks/useKeyboard.ts"], "mappings": ";;AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SACEC,QAAQ,EAIRC,QAAQ,QACH,cAAc;AACrB,SACEC,OAAO,EACPC,mBAAmB,EACnBC,cAAc,EACdC,kBAAkB,QACb,yBAAyB;AAChC,SAASC,cAAc,EAAEC,aAAa,QAAQ,cAAc;AAE5D,MAAMC,qBAAqB,GAAG;EAC5BC,aAAa,EAAER,QAAQ,CAACS,MAAM,CAAC;IAC7BC,GAAG,EAAE,kBAAkB;IACvBC,OAAO,EAAE,iBAAiB;IAC1BC,OAAO,EAAE;EACX,CAAC,CAAsB;EACvBC,aAAa,EAAEb,QAAQ,CAACS,MAAM,CAAC;IAC7BC,GAAG,EAAE,kBAAkB;IACvBC,OAAO,EAAE,iBAAiB;IAC1BC,OAAO,EAAE;EACX,CAAC;AACH,CAAC;AAED,OAAO,MAAME,WAAW,GAAGA,CAAA,KAAM;EAC/B;EACA,MAAMC,0BAA0B,GAAGZ,cAAc,CAAC,KAAK,CAAC;EACxD,MAAMa,aAAa,GAAGb,cAAc,CAClCE,cAAc,CAACY,YACjB,CAAC;EACD,MAAMC,cAAc,GAAGf,cAAc,CAAC,CAAC,CAAC;EACxC,MAAMgB,uBAAuB,GAC3BhB,cAAc,CAAsB,UAAU,CAAC;EACjD,MAAMiB,yBAAyB,GAAGjB,cAAc,CAAC,GAAG,CAAC;EACrD;EACA,MAAMkB,4BAA4B,GAAGlB,cAAc,CAAQ,EAAE,CAAC;EAC9D;;EAEA;EACA,MAAMmB,mBAAmB,GAAGlB,kBAAkB,CAC5C,CACEmB,KAAqB,EACrBC,MAAc,EACdC,QAAgB,EAChBC,MAA2B,EAC3BC,YAAqB,KAClB;IACH,IAAIJ,KAAK,KAAKlB,cAAc,CAACuB,KAAK,IAAI,CAACb,0BAA0B,CAACc,KAAK,EAAE;MACvE;AACR;AACA;AACA;AACA;MACQR,4BAA4B,CAACQ,KAAK,GAAG,CAACN,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,CAAC;MACtE;IACF;IACAR,cAAc,CAACW,KAAK,GAClBN,KAAK,KAAKlB,cAAc,CAACuB,KAAK,GAAGJ,MAAM,GAAGN,cAAc,CAACW,KAAK;;IAEhE;AACN;AACA;AACA;IACM,IAAIF,YAAY,EAAE;MAChBT,cAAc,CAACW,KAAK,GAAGX,cAAc,CAACW,KAAK,GAAGF,YAAY;IAC5D;IAEAP,yBAAyB,CAACS,KAAK,GAAGJ,QAAQ;IAC1CN,uBAAuB,CAACU,KAAK,GAAGH,MAAM;IACtCV,aAAa,CAACa,KAAK,GAAGN,KAAK;IAC3BF,4BAA4B,CAACQ,KAAK,GAAG,EAAE;EACzC,CAAC,EACD,EACF,CAAC;EACD;;EAEA;EACA/B,SAAS,CAAC,MAAM;IACd,MAAMgC,oBAAoB,GAAIC,KAAoB,IAAK;MACrD9B,OAAO,CAACqB,mBAAmB,CAAC,CAC1BjB,cAAc,CAACuB,KAAK,EACpBG,KAAK,CAACC,cAAc,CAACR,MAAM,EAC3BO,KAAK,CAACN,QAAQ,EACdM,KAAK,CAACL,MAAM,EACZpB,aAAa,GACXyB,KAAK,CAACC,cAAc,CAACR,MAAM,GAC3BO,KAAK,CAACC,cAAc,CAACC,OACzB,CAAC;IACH,CAAC;IACD,MAAMC,oBAAoB,GAAIH,KAAoB,IAAK;MACrD9B,OAAO,CAACqB,mBAAmB,CAAC,CAC1BjB,cAAc,CAAC8B,MAAM,EACrBJ,KAAK,CAACC,cAAc,CAACR,MAAM,EAC3BO,KAAK,CAACN,QAAQ,EACdM,KAAK,CAACL,MACR,CAAC;IACH,CAAC;IAED,MAAMU,gBAAgB,GAAGrC,QAAQ,CAACsC,WAAW,CAC3C9B,qBAAqB,CAACC,aAAa,EACnCsB,oBACF,CAAC;IAED,MAAMQ,gBAAgB,GAAGvC,QAAQ,CAACsC,WAAW,CAC3C9B,qBAAqB,CAACM,aAAa,EACnCqB,oBACF,CAAC;IAED,OAAO,MAAM;MACXE,gBAAgB,CAACG,MAAM,CAAC,CAAC;MACzBD,gBAAgB,CAACC,MAAM,CAAC,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,CAACjB,mBAAmB,CAAC,CAAC;;EAEzB;AACF;AACA;AACA;AACA;EACEpB,mBAAmB,CACjB,MAAMa,0BAA0B,CAACc,KAAK,EACtCW,MAAM,IAAI;IACR,MAAMC,MAAM,GAAGpB,4BAA4B,CAACQ,KAAK;IACjD,IAAIW,MAAM,IAAIC,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MAC/BpB,mBAAmB,CAACmB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IACjE;EACF,CAAC,EACD,EACF,CAAC;EACD;;EAEA,OAAO;IACLlB,KAAK,EAAEP,aAAa;IACpBQ,MAAM,EAAEN,cAAc;IACtByB,eAAe,EAAExB,uBAAuB;IACxCyB,iBAAiB,EAAExB,yBAAyB;IAC5CL;EACF,CAAC;AACH,CAAC", "ignoreList": []}