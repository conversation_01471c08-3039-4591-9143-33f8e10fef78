import { supabase } from '../lib/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface UserMessage {
  id: string;
  user_id: string;
  contact_name: string;
  company: string;
  avatar_url?: string;
  last_message: string;
  timestamp: string;
  is_unread: boolean;
}

export interface UserApplication {
  id: string;
  user_id: string;
  job_id: string;
  job_title: string;
  company: string;
  logo_url?: string;
  location?: string;
  pay?: string;
  job_image?: string;
  distance?: string;
  tags?: string[];
  description?: string;
  qualifications?: string[];
  requirements?: string[];
  company_logo?: string;
  job_url?: string;
  posted_date?: string;
  ai_explanation?: string;
  status: 'applying' | 'applied' | 'interviewing' | 'offered' | 'rejected' | 'accepted' | 'withdrawn';
  status_color: string;
  applied_at: string;
  response_message?: string;
  responded_at?: string;
  cover_letter_attached: boolean;
  notes?: string;
}

export interface UserPreferences {
  id: string;
  user_id: string;
  theme_preference: 'light' | 'dark' | 'system';
  notification_settings: {
    push: boolean;
    email: boolean;
    sms: boolean;
  };
  privacy_settings: {
    profile_visible: boolean;
    location_sharing: boolean;
  };
  app_settings: Record<string, any>;
}

export interface QuickJob {
  id: string;
  poster_id: string;
  title: string;
  description: string;
  location?: string;
  distance?: string;
  pay: string;
  category: string;
  urgency: 'low' | 'medium' | 'high';
  status: 'open' | 'in_progress' | 'completed' | 'cancelled';
  time_posted: string;
  estimated_duration?: string;
  requirements?: string[];
}

export interface UserCoverLetter {
  id: string;
  user_id: string;
  job_id: string;
  content?: string;
  file_url?: string;
  file_name?: string;
  file_size?: number;
  is_attached: boolean;
}

class UserDataService {
  // Messages
  async getUserMessages(userId: string): Promise<UserMessage[]> {
    try {
      const { data, error } = await supabase
        .from('user_messages')
        .select('*')
        .eq('user_id', userId)
        .order('timestamp', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching user messages:', error);
      return [];
    }
  }

  async createUserMessage(message: Omit<UserMessage, 'id'>): Promise<UserMessage | null> {
    try {
      const { data, error } = await supabase
        .from('user_messages')
        .insert([message])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating user message:', error);
      return null;
    }
  }

  async markMessageAsRead(messageId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_messages')
        .update({ is_unread: false })
        .eq('id', messageId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error marking message as read:', error);
      return false;
    }
  }

  // Applications
  async getUserApplications(userId: string): Promise<UserApplication[]> {
    try {
      const { data, error } = await supabase
        .from('user_applications')
        .select('*')
        .eq('user_id', userId)
        .order('applied_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching user applications:', error);
      return [];
    }
  }

  async createUserApplication(application: Omit<UserApplication, 'id'>): Promise<UserApplication | null> {
    try {
      const { data, error } = await supabase
        .from('user_applications')
        .insert([application])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating user application:', error);
      return null;
    }
  }

  async addUserApplication(application: Omit<UserApplication, 'id'>): Promise<UserApplication | null> {
    try {
      const { data, error } = await supabase
        .from('user_applications')
        .insert([application])
        .select()
        .single();

      if (error) {
        console.error('Error adding user application:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in addUserApplication:', error);
      return null;
    }
  }

  async updateUserApplication(userId: string, jobId: string, updates: Partial<UserApplication>): Promise<UserApplication | null> {
    try {
      const { data, error } = await supabase
        .from('user_applications')
        .update(updates)
        .eq('user_id', userId)
        .eq('job_id', jobId)
        .select()
        .single();

      if (error) {
        console.error('Error updating user application:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in updateUserApplication:', error);
      return null;
    }
  }

  async updateApplicationStatus(applicationId: string, status: UserApplication['status'], statusColor?: string): Promise<boolean> {
    try {
      const updateData: any = { status };
      if (statusColor) updateData.status_color = statusColor;

      const { error } = await supabase
        .from('user_applications')
        .update(updateData)
        .eq('id', applicationId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error updating application status:', error);
      return false;
    }
  }

  // User Preferences
  async getUserPreferences(userId: string): Promise<UserPreferences | null> {
    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "not found"
      return data;
    } catch (error) {
      console.error('Error fetching user preferences:', error);
      return null;
    }
  }

  async createOrUpdateUserPreferences(preferences: Omit<UserPreferences, 'id'>): Promise<UserPreferences | null> {
    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .upsert([preferences], { onConflict: 'user_id' })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating/updating user preferences:', error);
      return null;
    }
  }

  // Quick Jobs
  async getQuickJobs(limit: number = 50): Promise<QuickJob[]> {
    try {
      const { data, error } = await supabase
        .from('quick_jobs')
        .select('*')
        .eq('status', 'open')
        .order('time_posted', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching quick jobs:', error);
      return [];
    }
  }

  async createQuickJob(job: Omit<QuickJob, 'id'>): Promise<QuickJob | null> {
    try {
      const { data, error } = await supabase
        .from('quick_jobs')
        .insert([job])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating quick job:', error);
      return null;
    }
  }

  // Cover Letters
  async getUserCoverLetter(userId: string, jobId: string): Promise<UserCoverLetter | null> {
    try {
      const { data, error } = await supabase
        .from('user_cover_letters')
        .select('*')
        .eq('user_id', userId)
        .eq('job_id', jobId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      console.error('Error fetching user cover letter:', error);
      return null;
    }
  }

  async saveUserCoverLetter(coverLetter: Omit<UserCoverLetter, 'id'>): Promise<UserCoverLetter | null> {
    try {
      const { data, error } = await supabase
        .from('user_cover_letters')
        .upsert([coverLetter], { onConflict: 'user_id,job_id' })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error saving user cover letter:', error);
      return null;
    }
  }

  // Swiped Jobs
  async getUserSwipedJobs(userId: string): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('user_swiped_jobs')
        .select('job_id')
        .eq('user_id', userId);

      if (error) throw error;
      return data?.map(item => item.job_id) || [];
    } catch (error) {
      console.error('Error fetching user swiped jobs:', error);
      return [];
    }
  }

  async addSwipedJob(userId: string, jobId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_swiped_jobs')
        .insert([{ user_id: userId, job_id: jobId }]);

      if (error && error.code !== '23505') throw error; // 23505 is unique constraint violation
      return true;
    } catch (error) {
      console.error('Error adding swiped job:', error);
      return false;
    }
  }

  // Bookmarks
  async getUserBookmarks(userId: string): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('user_bookmarks')
        .select('*')
        .eq('user_id', userId)
        .order('bookmarked_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching user bookmarks:', error);
      return [];
    }
  }

  async addBookmark(userId: string, jobData: any): Promise<boolean> {
    try {
      const bookmark = {
        user_id: userId,
        job_id: jobData.id,
        job_title: jobData.title,
        company: jobData.company,
        logo_url: jobData.logo_url
      };

      const { error } = await supabase
        .from('user_bookmarks')
        .insert([bookmark]);

      if (error && error.code !== '23505') throw error;
      return true;
    } catch (error) {
      console.error('Error adding bookmark:', error);
      return false;
    }
  }

  async removeBookmark(userId: string, jobId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_bookmarks')
        .delete()
        .eq('user_id', userId)
        .eq('job_id', jobId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error removing bookmark:', error);
      return false;
    }
  }

  // Migration helpers - to move data from AsyncStorage to Supabase
  async migrateLocalDataToSupabase(userId: string): Promise<void> {
    try {
      // Migrate swiped jobs from AsyncStorage
      const swipedJobsKey = `swipedJobs_${userId}`;
      const localSwipedJobs = await AsyncStorage.getItem(swipedJobsKey);
      if (localSwipedJobs) {
        const jobIds = JSON.parse(localSwipedJobs);
        for (const jobId of jobIds) {
          await this.addSwipedJob(userId, jobId);
        }
        await AsyncStorage.removeItem(swipedJobsKey);
      }

      // Migrate theme preference
      const themeKey = 'theme';
      const localTheme = await AsyncStorage.getItem(themeKey);
      if (localTheme) {
        const preferences = {
          user_id: userId,
          theme_preference: localTheme as 'light' | 'dark' | 'system',
          notification_settings: { push: true, email: true, sms: false },
          privacy_settings: { profile_visible: true, location_sharing: true },
          app_settings: {}
        };
        await this.createOrUpdateUserPreferences(preferences);
        await AsyncStorage.removeItem(themeKey);
      }

      // Migrate application data
      const applicationDataKey = 'applicationData';
      const localApplicationData = await AsyncStorage.getItem(applicationDataKey);
      if (localApplicationData) {
        const applications = JSON.parse(localApplicationData);
        for (const app of applications) {
          const userApp: Omit<UserApplication, 'id'> = {
            user_id: userId,
            job_id: app.id || app.jobId,
            job_title: app.title || app.jobTitle,
            company: app.company,
            logo_url: app.logo || app.logoUrl,
            status: app.status || 'applied',
            status_color: app.statusColor || '#FFC107',
            applied_at: app.appliedAt || new Date().toISOString(),
            cover_letter_attached: app.coverLetterAttached || false,
            notes: app.notes
          };
          await this.createUserApplication(userApp);
        }
        await AsyncStorage.removeItem(applicationDataKey);
      }

      console.log('Local data migration completed successfully');
    } catch (error) {
      console.error('Error migrating local data to Supabase:', error);
    }
  }

  // Cleanup method to remove all local storage data after migration
  async cleanupLocalStorage(): Promise<void> {
    try {
      const keysToRemove = [
        'theme',
        'applicationData',
        'coverLetterStatus',
        'rememberMe',
        'userEmail'
      ];

      // Remove keys that start with specific prefixes
      const allKeys = await AsyncStorage.getAllKeys();
      const keysToRemoveWithPrefix = allKeys.filter(key => 
        key.startsWith('swipedJobs_') || 
        key.startsWith('coverLetter_') ||
        key.startsWith('bookmark_') ||
        key.startsWith('job_filters_')
      );

      await AsyncStorage.multiRemove([...keysToRemove, ...keysToRemoveWithPrefix]);
      console.log('Local storage cleanup completed');
    } catch (error) {
      console.error('Error cleaning up local storage:', error);
    }
  }
}

export const userDataService = new UserDataService();
export default userDataService;