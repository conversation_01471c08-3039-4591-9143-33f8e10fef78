// Simple test script to verify AI evaluation is working
const OPENROUTER_API_KEY = '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';
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

const testJob = {
  title: 'Senior Software Engineer',
  company: 'TechCorp',
  location: 'San Francisco, CA',
  pay: '$120,000 - $160,000',
  description: 'We are looking for a Senior Software Engineer to join our team. You will be responsible for building scalable web applications using React, Node.js, and PostgreSQL. The ideal candidate should have 5+ years of experience in full-stack development.',
  requirements: ['5+ years of JavaScript experience', 'Experience with React and Node.js', 'Database design knowledge'],
  qualifications: ['Bachelor\'s degree in Computer Science', 'Strong problem-solving skills', 'Team player'],
  tags: ['Full-time', 'Remote', 'Engineering']
};

async function testAIEvaluation() {
  try {
    console.log('Testing AI evaluation for job:', testJob.title);

    const prompt = `Please evaluate this job posting and provide a comprehensive analysis:

Job Title: ${testJob.title}
Company: ${testJob.company}
Location: ${testJob.location}
Compensation: ${testJob.pay}
Description: ${testJob.description}
Requirements: ${testJob.requirements.join(', ')}
Qualifications: ${testJob.qualifications.join(', ')}
Tags: ${testJob.tags.join(', ')}

Please analyze this job and provide:
1. A score from 0-100 (where 100 is excellent)
2. Key strengths of this position
3. Potential concerns or drawbacks
4. A brief summary of the role
5. Recommendations for candidates
6. Overall fit level (excellent/good/fair/poor)

Return your response as a JSON object with this structure:
{
  "score": number,
  "strengths": ["strength1", "strength2", ...],
  "concerns": ["concern1", "concern2", ...],
  "summary": "brief description",
  "recommendations": ["rec1", "rec2", ...],
  "fit_level": "excellent|good|fair|poor"
}

Focus on: job market competitiveness, compensation fairness, growth opportunities, work-life balance indicators, and skill development potential.`;

    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://jobbify.app',
        'X-Title': 'Jobbify AI Job Evaluation Test'
      },
      body: JSON.stringify({
        model: 'featherless/qwerky-72b:free',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const aiResponse = data.choices[0].message.content;

    console.log('\n=== AI EVALUATION RESPONSE ===');
    console.log(aiResponse);

    // Try to parse JSON
    try {
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : aiResponse;
      const evaluation = JSON.parse(jsonString);
      
      console.log('\n=== PARSED EVALUATION ===');
      console.log('Score:', evaluation.score);
      console.log('Fit Level:', evaluation.fit_level);
      console.log('Summary:', evaluation.summary);
      console.log('Strengths:', evaluation.strengths);
      console.log('Concerns:', evaluation.concerns);
      console.log('Recommendations:', evaluation.recommendations);
    } catch (parseError) {
      console.log('\nFailed to parse as JSON, raw response above');
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
}

testAIEvaluation();
