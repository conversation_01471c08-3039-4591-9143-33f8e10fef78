name: Nightly Cache Cleanup

on:
  schedule:
    # Run every night at 03:00 UTC
    - cron: '0 3 * * *'
  workflow_dispatch: # Allow manual triggering

jobs:
  cleanup:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        cd job-api
        pip install -r requirements.txt
        
    - name: Install Redis (for testing)
      run: |
        sudo apt-get update
        sudo apt-get install -y redis-server
        sudo systemctl start redis-server
        
    - name: Run nightly cleanup
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_KEY: ${{ secrets.SUPABASE_KEY }}
        REDIS_URL: redis://localhost:6379
      run: |
        cd job-api
        python cleanup_cache.py
        
    - name: Notify on failure
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: 'Nightly cache cleanup job failed!'
        fields: repo,message,commit,author,action,eventName,ref,workflow
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        
    - name: Notify on success
      if: success()
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: 'Nightly cache cleanup completed successfully!'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
