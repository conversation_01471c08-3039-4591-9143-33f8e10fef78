{"dashboard": {"id": null, "title": "Job API Monitoring Dashboard", "tags": ["job-api", "monitoring"], "timezone": "browser", "panels": [{"id": 1, "title": "API Calls per Second", "type": "stat", "targets": [{"expr": "sum(rate(api_calls_total[5m]))", "legendFormat": "Total API Calls/sec"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5}, {"color": "red", "value": 10}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "<PERSON><PERSON>", "type": "stat", "targets": [{"expr": "rate(cache_hits_total[5m]) / (rate(cache_hits_total[5m]) + rate(cache_misses_total[5m]))", "legendFormat": "Hit Ratio"}], "fieldConfig": {"defaults": {"unit": "percentunit", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 0.5}, {"color": "green", "value": 0.8}]}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "API Error Rate", "type": "stat", "targets": [{"expr": "sum(rate(api_errors_total[5m]))", "legendFormat": "Errors/sec"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.1}, {"color": "red", "value": 1}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Response Time (95th percentile)", "type": "stat", "targets": [{"expr": "histogram_quantile(0.95, rate(response_time_ms_bucket[5m]))", "legendFormat": "95th percentile"}], "fieldConfig": {"defaults": {"unit": "ms", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1000}, {"color": "red", "value": 5000}]}}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "API Calls by Service", "type": "timeseries", "targets": [{"expr": "sum(rate(api_calls_total[5m])) by (api)", "legendFormat": "{{ api }}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 6, "title": "<PERSON><PERSON>", "type": "timeseries", "targets": [{"expr": "rate(cache_hits_total[5m])", "legendFormat": "Cache Hits/sec"}, {"expr": "rate(cache_misses_total[5m])", "legendFormat": "<PERSON><PERSON>/sec"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 7, "title": "Job Quality Score Distribution", "type": "histogram", "targets": [{"expr": "rate(job_quality_score_bucket[5m])", "legendFormat": "Quality Score"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 8, "title": "API Quota Usage", "type": "bargauge", "targets": [{"expr": "api_quota_usage_percent", "legendFormat": "{{ api }}"}], "fieldConfig": {"defaults": {"unit": "percent", "max": 100, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s"}}