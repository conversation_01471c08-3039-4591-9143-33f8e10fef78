{"version": 3, "names": ["_react", "require", "_constants", "useBottomSheetTimingConfigs", "configs", "useMemo", "_configs", "easing", "ANIMATION_EASING", "duration", "ANIMATION_DURATION", "reduceMotion", "exports"], "sourceRoot": "../../../src", "sources": ["hooks/useBottomSheetTimingConfigs.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAMA,IAAAC,UAAA,GAAAD,OAAA;AAEA;AACA;AACA;AACA;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAME,2BAA2B,GAAIC,OAAqB,IAAK;EACpE,OAAO,IAAAC,cAAO,EAAC,MAAM;IACnB,MAAMC,QAAsB,GAAG;MAC7BC,MAAM,EAAEH,OAAO,CAACG,MAAM,IAAIC,2BAAgB;MAC1CC,QAAQ,EAAEL,OAAO,CAACK,QAAQ,IAAIC,6BAAkB;MAChDC,YAAY,EAAEP,OAAO,CAACO;IACxB,CAAC;IAED,OAAOL,QAAQ;EACjB,CAAC,EAAE,CAACF,OAAO,CAACK,QAAQ,EAAEL,OAAO,CAACG,MAAM,EAAEH,OAAO,CAACO,YAAY,CAAC,CAAC;AAC9D,CAAC;AAACC,OAAA,CAAAT,2BAAA,GAAAA,2BAAA", "ignoreList": []}