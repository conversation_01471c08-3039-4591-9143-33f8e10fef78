{"version": 3, "names": ["_invariant", "_interopRequireDefault", "require", "_react", "_constants", "e", "__esModule", "default", "usePropsValidator", "index", "snapPoints", "enableDynamicSizing", "topInset", "bottomInset", "useMemo", "_snapPoints", "get", "invariant", "map", "snapPoint", "_snapPoint", "Number", "parseInt", "replace", "INITIAL_SNAP_POINT", "length", "exports"], "sourceRoot": "../../../src", "sources": ["hooks/usePropsValidator.ts"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAEA,IAAAE,UAAA,GAAAF,OAAA;AAAyE,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEzE;AACA;AACA;AACA;;AAEO,MAAMG,iBAAiB,GAAGA,CAAC;EAChCC,KAAK;EACLC,UAAU;EACVC,mBAAmB;EACnBC,QAAQ;EACRC;AAIF,CAAC,KAAK;EACJ,IAAAC,cAAO,EAAC,MAAM;IACZ;IACA,MAAMC,WAAW,GAAGL,UAAU,GAC1B,KAAK,IAAIA,UAAU,GACjBA,UAAU,CAACM,GAAG,CAAC,CAAC,GAChBN,UAAU,GACZ,EAAE;IACN,IAAAO,kBAAS,EACPF,WAAW,IAAIJ,mBAAmB,EAClC,wEACF,CAAC;IAEDI,WAAW,CAACG,GAAG,CAACC,SAAS,IAAI;MAC3B,MAAMC,UAAU,GACd,OAAOD,SAAS,KAAK,QAAQ,GACzBA,SAAS,GACTE,MAAM,CAACC,QAAQ,CAACH,SAAS,CAACI,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;MAErD,IAAAN,kBAAS,EACPG,UAAU,GAAG,CAAC,IAAIA,UAAU,KAAKI,6BAAkB,EACnD,eAAeL,SAAS,qGAC1B,CAAC;IACH,CAAC,CAAC;IAEF,IAAAF,kBAAS,EACP,OAAO,IAAIF,WAAW,IAAIA,WAAW,CAACU,MAAM,GAAG,CAAC,IAAId,mBAAmB,EACvE,mFACF,CAAC;IACD;;IAEA;IACA,IAAAM,kBAAS,EACP,OAAOR,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,WAAW,EACzD,uEACF,CAAC;IAED,IAAAQ,kBAAS,EACPN,mBAAmB,KAChB,OAAOF,KAAK,KAAK,QAAQ,GACtBA,KAAK,IAAI,CAAC,CAAC,IAAIA,KAAK,IAAIM,WAAW,CAACU,MAAM,GAAG,CAAC,GAC9C,IAAI,CAAC,EACX,oGACEV,WAAW,CAACU,MAAM,GAAG,CAAC,EAE1B,CAAC;IACD;;IAEA;IACA,IAAAR,kBAAS,EACP,OAAOL,QAAQ,KAAK,QAAQ,IAAI,OAAOA,QAAQ,KAAK,WAAW,EAC/D,0EACF,CAAC;IACD,IAAAK,kBAAS,EACP,OAAOJ,WAAW,KAAK,QAAQ,IAAI,OAAOA,WAAW,KAAK,WAAW,EACrE,6EACF,CAAC;IACD;;IAEA;EACF,CAAC,EAAE,CAACJ,KAAK,EAAEC,UAAU,EAAEE,QAAQ,EAAEC,WAAW,EAAEF,mBAAmB,CAAC,CAAC;AACrE,CAAC;AAACe,OAAA,CAAAlB,iBAAA,GAAAA,iBAAA", "ignoreList": []}