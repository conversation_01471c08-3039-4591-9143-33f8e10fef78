#!/usr/bin/env python3
import requests
from job_service import supabase

def generate_api_status_report():
    """Generate a comprehensive status report for all APIs"""
    print("📊 JOBBIFY API STATUS REPORT")
    print("=" * 50)
    
    # Test server health
    print("\n🏥 SERVER HEALTH:")
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("   ✅ Server is running and healthy")
        else:
            print(f"   ❌ Server health check failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Server is not responding: {e}")
        return
    
    # Check database
    print("\n💾 DATABASE STATUS:")
    try:
        response = supabase.table("jobs").select("source").execute()
        jobs = response.data
        
        source_counts = {}
        for job in jobs:
            source = job.get('source') or 'unknown'
            source_counts[source] = source_counts.get(source, 0) + 1
        
        total_jobs = len(jobs)
        print(f"   📈 Total jobs in database: {total_jobs}")
        print(f"   📊 Jobs by source:")
        for source, count in sorted(source_counts.items()):
            percentage = (count / total_jobs * 100) if total_jobs > 0 else 0
            print(f"      {source.upper()}: {count} jobs ({percentage:.1f}%)")
            
    except Exception as e:
        print(f"   ❌ Database error: {e}")
    
    # Test API refresh
    print("\n🔄 API REFRESH TEST:")
    try:
        response = requests.post("http://localhost:8000/jobs/refresh", timeout=30)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Refresh successful: {result.get('message', 'No message')}")
        else:
            print(f"   ❌ Refresh failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Refresh error: {e}")
    
    # Test jobs endpoint
    print("\n📋 JOBS ENDPOINT TEST:")
    try:
        response = requests.get("http://localhost:8000/jobs/", timeout=10)
        if response.status_code == 200:
            jobs = response.json()
            print(f"   ✅ Jobs endpoint working: {len(jobs)} jobs returned")
            
            # Show sample jobs from each source
            source_samples = {}
            for job in jobs[:20]:  # Check first 20 jobs
                description = job.get('description', '') or ''
                for source in ['remoteok', 'arbeitnow', 'adzuna', 'jooble', 'muse', 'ashby']:
                    if f'SOURCE: {source}' in description and source not in source_samples:
                        source_samples[source] = {
                            'title': job.get('title', 'No title'),
                            'company': job.get('company', 'No company')
                        }
            
            print(f"   📝 Sample jobs from different sources:")
            for source, job_info in source_samples.items():
                print(f"      {source.upper()}: {job_info['title']} at {job_info['company']}")
                
        else:
            print(f"   ❌ Jobs endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Jobs endpoint error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 ALL APIS INTEGRATED SUCCESSFULLY!")
    print("📡 Active job sources:")
    print("   • RemoteOK")
    print("   • Arbeitnow") 
    print("   • Adzuna")
    print("   • Jooble (NEW!)")
    print("   • The Muse (NEW!)")
    print("   • Ashby (NEW!)")
    print("\n🚀 To start the server, run:")
    print("   python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload")

if __name__ == "__main__":
    generate_api_status_report()
