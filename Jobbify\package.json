{"name": "jobbify-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ashby": "node scripts/test-ashby-integration.js", "test:db": "node test-database-fixes.js"}, "dependencies": {"@expo-google-fonts/poppins": "^0.4.0", "@gorhom/bottom-sheet": "^5.1.6", "@motionone/dom": "^10.18.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/slider": "^4.5.7", "@react-native-google-signin/google-signin": "^14.0.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.49.4", "@types/ramda": "^0.30.2", "axios": "^1.5.1", "base64-arraybuffer": "^1.0.2", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "events": "^3.3.0", "expo": "53.0.10", "expo-app-loading": "^2.1.1", "expo-auth-session": "~6.2.0", "expo-av": "~15.1.5", "expo-blur": "~14.1.5", "expo-constants": "~17.1.6", "expo-document-picker": "~13.1.5", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-keep-awake": "~14.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-location": "^18.1.5", "expo-router": "~5.0.7", "expo-secure-store": "~14.2.3", "expo-speech": "~13.1.7", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-web-browser": "~14.1.6", "https-browserify": "^1.0.0", "moti": "^0.30.0", "ramda": "^0.31.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-reanimated": "~3.17.4", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@expo/cli": "^0.24.14", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.2", "jest": "^29.7.0", "node-fetch": "^2.7.0", "react-test-renderer": "^19.0.0", "typescript": "^5.1.3"}, "jest": {"preset": "react-native", "setupFilesAfterEnv": ["<rootDir>/jest-setup.js"], "transformIgnorePatterns": ["node_modules/(?!(react-native|@react-native|expo|@expo|@unimodules|unimodules|sentry-expo|native-base|react-clone-referenced-element|@react-native-community|@react-navigation|@supabase))"], "testMatch": ["**/__tests__/**/*.(js|jsx|ts|tsx)", "**/*.(test|spec).(js|jsx|ts|tsx)"], "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json"], "moduleNameMapper": {"^@/(.*)$": "<rootDir>/$1"}, "collectCoverageFrom": ["**/*.{js,jsx,ts,tsx}", "!**/node_modules/**", "!**/coverage/**", "!**/*.config.js"]}, "private": true}