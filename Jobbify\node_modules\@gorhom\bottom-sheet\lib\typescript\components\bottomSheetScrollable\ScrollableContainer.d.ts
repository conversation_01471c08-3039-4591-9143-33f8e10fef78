import React, { type FC } from 'react';
import type { SimultaneousGesture } from 'react-native-gesture-handler';
interface ScrollableContainerProps {
    nativeGesture?: SimultaneousGesture;
    ScrollableComponent: FC<any>;
}
export declare const ScrollableContainer: React.ForwardRefExoticComponent<ScrollableContainerProps & React.RefAttributes<never>>;
export {};
//# sourceMappingURL=ScrollableContainer.d.ts.map