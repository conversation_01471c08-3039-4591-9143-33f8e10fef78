# Profile RLS Issue Fix

## Problem Description

Users were experiencing a "JSON object requested, multiple (or no) rows returned" error during the signup process, specifically when trying to verify their profile after account creation.

## Root Cause

The `profiles` table was missing an **INSERT** policy in its Row Level Security (RLS) configuration. While the table had:
- ✅ SELECT policy: "Users can view their own profile"
- ✅ UPDATE policy: "Users can update their own profile"
- ❌ **Missing INSERT policy**

This caused a timing issue where:
1. User signs up successfully
2. Database trigger (`handle_new_user()`) creates profile with `SECURITY DEFINER`
3. User tries to access their profile immediately
4. <PERSON><PERSON> blocks access because there's no proper INSERT policy
5. Profile verification fails with "0 rows" error

## Solution

Added two INSERT policies to the `profiles` table:

```sql
-- Allow users to insert their own profile
CREATE POLICY "Users can insert their own profile"
  ON profiles FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Allow system/trigger to insert profiles
CREATE POLICY "System can insert profiles"
  ON profiles FOR INSERT
  WITH CHECK (true);
```

## Files Created/Modified

1. **Migration File**: `database/migrations/002_fix_profiles_insert_policy.sql`
2. **Fix Script**: `scripts/fix-profiles-rls.js`
3. **Quick SQL Fix**: `fix-profiles-rls.sql`
4. **Documentation**: `docs/PROFILE_RLS_FIX.md` (this file)

## How to Apply the Fix

### Option 1: Quick Fix (Recommended)
1. Open Supabase Dashboard
2. Go to SQL Editor
3. Run the contents of `fix-profiles-rls.sql`

### Option 2: Using Migration Script
```bash
cd scripts
node fix-profiles-rls.js
```

### Option 3: Manual Migration
```bash
cd scripts
node run-migration.js 002_fix_profiles_insert_policy
```

## Verification

After applying the fix:
1. Check that policies exist:
   ```sql
   SELECT policyname, cmd FROM pg_policies WHERE tablename = 'profiles';
   ```
2. Test signup flow - profile verification should now work immediately
3. No more "JSON object requested, multiple (or no) rows returned" errors

## Related Files

- `app/(auth)/signup.tsx` - Contains retry logic for this RLS timing issue
- `database/schema.sql` - Main schema with RLS policies
- `docs/supabase-create-functions.sql` - Contains the trigger function
- `utils/authDebug.ts` - Testing utilities for auth flow

## Prevention

When creating new tables with RLS:
1. Always include INSERT policies if users need to create records
2. Consider both user-level and system-level access patterns
3. Test the complete user flow, not just individual operations
4. Use `SECURITY DEFINER` functions carefully and ensure proper policies exist