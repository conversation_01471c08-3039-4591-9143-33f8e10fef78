import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';

interface WorkStyleCardProps {
  preferences: any;
  onNext: (data: any) => void;
  onPrevious?: () => void;
  theme: string;
  themeColors: any;
}

const WORK_STYLES = [
  {
    id: 'remote',
    title: 'Remote',
    subtitle: 'Work from anywhere',
    icon: 'home',
    color: '#10B981'
  },
  {
    id: 'hybrid',
    title: 'Hybrid',
    subtitle: 'Mix of remote and office',
    icon: 'refresh',
    color: '#F59E0B'
  },
  {
    id: 'in-person',
    title: 'In-Person',
    subtitle: 'On-site at office',
    icon: 'building',
    color: '#3B82F6'
  }
];

export default function WorkStyleCard({ 
  preferences, 
  onNext, 
  onPrevious, 
  theme, 
  themeColors 
}: WorkStyleCardProps) {
  const [selectedWorkStyle, setSelectedWorkStyle] = useState(preferences.workStyle || '');

  const handleNext = () => {
    if (!selectedWorkStyle) {
      Alert.alert('Work Style Required', 'Please select your preferred work style.');
      return;
    }
    onNext({ workStyle: selectedWorkStyle });
  };

  const renderWorkStyleOption = (workStyle: typeof WORK_STYLES[0]) => {
    const isSelected = selectedWorkStyle === workStyle.id;
    
    return (
      <TouchableOpacity
        key={workStyle.id}
        style={[
          styles.workStyleCard,
          {
            backgroundColor: isSelected ? workStyle.color : themeColors.card,
            borderColor: isSelected ? workStyle.color : themeColors.border,
          }
        ]}
        onPress={() => setSelectedWorkStyle(workStyle.id)}
      >
        <View style={[
          styles.iconContainer,
          { backgroundColor: isSelected ? 'rgba(255,255,255,0.2)' : workStyle.color + '20' }
        ]}>
          <FontAwesome 
            name={workStyle.icon as any} 
            size={24} 
            color={isSelected ? '#FFFFFF' : workStyle.color} 
          />
        </View>
        
        <View style={styles.textContainer}>
          <Text style={[
            styles.workStyleTitle,
            { color: isSelected ? '#FFFFFF' : themeColors.text }
          ]}>
            {workStyle.title}
          </Text>
          <Text style={[
            styles.workStyleSubtitle,
            { color: isSelected ? 'rgba(255,255,255,0.8)' : themeColors.textSecondary }
          ]}>
            {workStyle.subtitle}
          </Text>
        </View>

        {isSelected && (
          <View style={styles.checkContainer}>
            <FontAwesome name="check-circle" size={20} color="#FFFFFF" />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <Animated.View entering={FadeInUp.delay(200)} style={styles.header}>
        <Text style={[styles.title, { color: themeColors.text }]}>
          What's your work style preference?
        </Text>
        <Text style={[styles.subtitle, { color: themeColors.textSecondary }]}>
          Choose how you prefer to work
        </Text>
      </Animated.View>

      <Animated.View entering={FadeInUp.delay(400)} style={styles.content}>
        {WORK_STYLES.map(renderWorkStyleOption)}
      </Animated.View>

      <Animated.View entering={FadeInDown.delay(600)} style={styles.buttonContainer}>
        <View style={styles.buttonRow}>
          {onPrevious && (
            <TouchableOpacity
              style={[styles.backButton, { borderColor: themeColors.border }]}
              onPress={onPrevious}
            >
              <FontAwesome name="arrow-left" size={16} color={themeColors.textSecondary} />
              <Text style={[styles.backText, { color: themeColors.textSecondary }]}>
                Back
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[
              styles.nextButton,
              { 
                backgroundColor: selectedWorkStyle ? themeColors.tint : themeColors.border,
                opacity: selectedWorkStyle ? 1 : 0.5
              },
              !onPrevious && styles.fullWidthButton
            ]}
            onPress={handleNext}
            disabled={!selectedWorkStyle}
          >
            <Text style={styles.nextText}>Continue</Text>
            <FontAwesome name="arrow-right" size={16} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
    paddingTop: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    gap: 16,
  },
  workStyleCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderRadius: 16,
    borderWidth: 2,
    minHeight: 80,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  textContainer: {
    flex: 1,
  },
  workStyleTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  workStyleSubtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  checkContainer: {
    marginLeft: 12,
  },
  buttonContainer: {
    paddingBottom: 32,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 2,
    gap: 8,
    flex: 1,
  },
  backText: {
    fontSize: 16,
    fontWeight: '600',
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
    flex: 2,
  },
  fullWidthButton: {
    flex: 1,
  },
  nextText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
