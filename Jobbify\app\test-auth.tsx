import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, SafeAreaView } from 'react-native';
import { authDebug } from '../utils/authDebug';
import { securityAudit } from '../utils/securityAudit';
import { useAppContext } from '../context/AppContext';
import { LightTheme, DarkTheme } from '../constants/Theme';
import { supabase } from '../lib/supabase';

export default function TestAuthScreen() {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testSignup = async () => {
    setIsLoading(true);
    addResult('🧪 Starting signup test...');
    
    try {
      const testEmail = `test-${Date.now()}@example.com`;
      const testPassword = 'password123';
      const testName = 'Test User';
      
      addResult(`📧 Testing with email: ${testEmail}`);
      
      const result = await authDebug.testSignup(testEmail, testPassword, testName);
      
      if (result.success) {
        addResult('✅ Signup test PASSED!');
        addResult(`👤 User ID: ${result.data?.user.id}`);
        addResult(`📝 Profile: ${result.data?.profile ? 'Created' : 'Not found'}`);
        addResult(`💼 Job Seeker Profile: ${result.data?.jobSeekerProfile ? 'Created' : 'Not found'}`);
      } else {
        addResult(`❌ Signup test FAILED: ${result.error}`);
      }
    } catch (error) {
      addResult(`💥 Test error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testDatabaseAccess = async () => {
    setIsLoading(true);
    addResult('🔍 Testing database access...');

    try {
      const result = await authDebug.testDatabaseAccess();

      if (result.success) {
        addResult('✅ Database access test PASSED!');
      } else {
        addResult(`❌ Database access test FAILED: ${result.error}`);
      }
    } catch (error) {
      addResult(`💥 Database test error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testDataIsolation = async () => {
    setIsLoading(true);
    addResult('🔒 Testing data isolation security...');

    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        addResult('❌ No authenticated user found. Please log in first.');
        return;
      }

      addResult(`👤 Testing data isolation for user: ${user.id}`);

      const result = await securityAudit.runFullSecurityAudit(user.id);

      if (result.success) {
        addResult('✅ Data isolation test PASSED! User can only see their own data.');
      } else {
        addResult('❌ Data isolation test FAILED! Security issues found:');
        if (result.userDataIsolation.issues > 0) {
          addResult(`   🚨 ${result.userDataIsolation.issues} tables have data leakage`);
        }
      }

      // Show detailed results
      if (result.userDataIsolation.results) {
        result.userDataIsolation.results.forEach((tableResult: any) => {
          const isSecure = tableResult.recordCount === tableResult.actualUserRecords;
          const status = isSecure ? '✅' : '❌';
          addResult(`   ${status} ${tableResult.table}: ${tableResult.recordCount} total, ${tableResult.actualUserRecords} user records`);
        });
      }

    } catch (error) {
      addResult(`💥 Security test error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
      <ScrollView style={styles.scrollView}>
        <Text style={[styles.title, { color: themeColors.text }]}>
          Authentication Test Suite
        </Text>
        
        <Text style={[styles.subtitle, { color: themeColors.textSecondary }]}>
          Use this screen to test the authentication fixes
        </Text>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, { backgroundColor: themeColors.tint }]}
            onPress={testSignup}
            disabled={isLoading}
          >
            <Text style={[styles.buttonText, { color: themeColors.background }]}>
              {isLoading ? 'Testing...' : 'Test Signup Process'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, { backgroundColor: themeColors.tint }]}
            onPress={testDatabaseAccess}
            disabled={isLoading}
          >
            <Text style={[styles.buttonText, { color: themeColors.background }]}>
              Test Database Access
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, { backgroundColor: themeColors.error }]}
            onPress={testDataIsolation}
            disabled={isLoading}
          >
            <Text style={[styles.buttonText, { color: themeColors.background }]}>
              🔒 Test Data Isolation Security
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.clearButton, { borderColor: themeColors.border }]}
            onPress={clearResults}
          >
            <Text style={[styles.clearButtonText, { color: themeColors.textSecondary }]}>
              Clear Results
            </Text>
          </TouchableOpacity>
        </View>

        <View style={[styles.resultsContainer, { backgroundColor: themeColors.card }]}>
          <Text style={[styles.resultsTitle, { color: themeColors.text }]}>
            Test Results:
          </Text>
          
          {testResults.length === 0 ? (
            <Text style={[styles.noResults, { color: themeColors.textSecondary }]}>
              No tests run yet. Click a button above to start testing.
            </Text>
          ) : (
            testResults.map((result, index) => (
              <Text
                key={index}
                style={[
                  styles.resultText,
                  { 
                    color: result.includes('❌') || result.includes('💥') 
                      ? themeColors.error 
                      : result.includes('✅') 
                        ? themeColors.success 
                        : themeColors.text 
                  }
                ]}
              >
                {result}
              </Text>
            ))
          )}
        </View>

        <View style={styles.infoContainer}>
          <Text style={[styles.infoTitle, { color: themeColors.text }]}>
            What this tests:
          </Text>
          <Text style={[styles.infoText, { color: themeColors.textSecondary }]}>
            • WebCrypto polyfill (should eliminate warnings)
          </Text>
          <Text style={[styles.infoText, { color: themeColors.textSecondary }]}>
            • Database trigger for profile creation
          </Text>
          <Text style={[styles.infoText, { color: themeColors.textSecondary }]}>
            • Row Level Security policies
          </Text>
          <Text style={[styles.infoText, { color: themeColors.textSecondary }]}>
            • Foreign key constraints
          </Text>
          <Text style={[styles.infoText, { color: themeColors.textSecondary }]}>
            • Job seeker profile creation
          </Text>
          <Text style={[styles.infoText, { color: themeColors.error }]}>
            • 🔒 Data isolation security (CRITICAL)
          </Text>
          <Text style={[styles.infoText, { color: themeColors.textSecondary }]}>
            • User can only see their own data
          </Text>
          <Text style={[styles.infoText, { color: themeColors.textSecondary }]}>
            • No data leakage between accounts
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 30,
    textAlign: 'center',
  },
  buttonContainer: {
    marginBottom: 30,
  },
  button: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  clearButton: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    alignItems: 'center',
  },
  clearButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  resultsContainer: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 30,
    minHeight: 200,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  noResults: {
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 20,
  },
  resultText: {
    fontSize: 14,
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  infoContainer: {
    marginBottom: 30,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    marginBottom: 4,
  },
});
