#!/usr/bin/env python3
"""
Cache Warming Cron Job
Pre-populates Redis cache with popular job searches to improve response times
"""

import asyncio
import httpx
import logging
from datetime import datetime
from typing import List, Dict, Any
from supabase_client import supabase
from cache import init_cache, cache_jobs
from personalized_search import PersonalizedSearchRequest, personalized_job_search

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CacheWarmer:
    def __init__(self):
        self.base_url = "http://localhost:8000"  # Update for production
        self.popular_searches_threshold = 5  # Minimum user count to be considered popular
        
    async def get_popular_search_patterns(self) -> List[Dict[str, Any]]:
        """Get top-20 most common preference signatures from database"""
        try:
            # Query cached_job_searches for popular patterns
            response = supabase.table("cached_job_searches").select(
                "search_params, user_count"
            ).gte("user_count", self.popular_searches_threshold).order(
                "user_count", desc=True
            ).limit(20).execute()
            
            if response.data:
                logger.info(f"📊 Found {len(response.data)} popular search patterns")
                return response.data
            else:
                logger.info("📊 No popular search patterns found, using defaults")
                return self._get_default_search_patterns()
                
        except Exception as e:
            logger.error(f"Error getting popular search patterns: {e}")
            return self._get_default_search_patterns()
    
    def _get_default_search_patterns(self) -> List[Dict[str, Any]]:
        """Default search patterns for initial cache warming"""
        return [
            {
                "search_params": {
                    "user_preferences": {
                        "preferred_locations": ["San Francisco, CA"],
                        "preferred_job_types": ["Full-time"],
                        "preferred_industries": ["Technology"],
                        "experience_level": "mid"
                    }
                },
                "user_count": 10
            },
            {
                "search_params": {
                    "user_preferences": {
                        "preferred_locations": ["Remote"],
                        "preferred_job_types": ["Full-time", "Remote"],
                        "preferred_industries": ["Technology", "Software"],
                        "experience_level": "senior"
                    }
                },
                "user_count": 8
            },
            {
                "search_params": {
                    "user_preferences": {
                        "preferred_locations": ["New York, NY"],
                        "preferred_job_types": ["Full-time"],
                        "preferred_industries": ["Finance"],
                        "experience_level": "mid"
                    }
                },
                "user_count": 7
            },
            {
                "search_params": {
                    "user_preferences": {
                        "preferred_locations": ["Los Angeles, CA"],
                        "preferred_job_types": ["Full-time"],
                        "preferred_industries": ["Healthcare"],
                        "experience_level": "entry"
                    }
                },
                "user_count": 6
            },
            {
                "search_params": {
                    "user_preferences": {
                        "preferred_locations": ["Remote"],
                        "preferred_job_types": ["Contract", "Freelance"],
                        "preferred_industries": ["Marketing & Advertising"],
                        "experience_level": "mid"
                    }
                },
                "user_count": 5
            }
        ]
    
    async def warm_cache_for_pattern(self, pattern: Dict[str, Any]) -> bool:
        """Warm cache for a specific search pattern"""
        try:
            user_preferences = pattern["search_params"]["user_preferences"]
            user_count = pattern["user_count"]
            
            # Extract search parameters
            locations = user_preferences.get("preferred_locations", ["Remote"])[:1]
            job_types = user_preferences.get("preferred_job_types", ["Full-time"])[:1]
            experience = user_preferences.get("experience_level", "mid")
            industries = user_preferences.get("preferred_industries", [])[:1]
            
            if not locations or not job_types:
                logger.warning(f"⚠️ Skipping invalid pattern: {pattern}")
                return False
            
            logger.info(f"🔥 Warming cache for: {locations[0]}, {job_types}, {experience}, {industries}")
            
            # Create a mock request to trigger the search
            mock_request = PersonalizedSearchRequest(
                user_id="cache-warmer",
                force_refresh=True,
                limit=100
            )
            
            # Temporarily insert user preferences for cache warmer
            temp_prefs = {
                "user_id": "cache-warmer",
                "preferred_locations": locations,
                "preferred_job_types": job_types,
                "preferred_industries": industries,
                "experience_level": experience,
                "min_salary": 50000,
                "max_salary": 200000,
                "plan": "pro"  # Bypass rate limits
            }
            
            # Insert temporary preferences
            supabase.table("user_job_preferences").upsert(temp_prefs).execute()
            
            # Call the personalized search endpoint
            result = await personalized_job_search(mock_request)
            
            # Clean up temporary preferences
            supabase.table("user_job_preferences").delete().eq("user_id", "cache-warmer").execute()
            
            if hasattr(result, 'jobs') and len(result.jobs) > 0:
                logger.info(f"✅ Cached {len(result.jobs)} jobs for popular pattern")
                return True
            else:
                logger.warning(f"⚠️ No jobs found for pattern: {locations[0]}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error warming cache for pattern: {e}")
            return False
    
    async def run_cache_warming(self) -> Dict[str, Any]:
        """Run the complete cache warming process"""
        start_time = datetime.now()
        logger.info("🔥 Starting cache warming process...")
        
        # Initialize cache connection
        await init_cache()
        
        # Get popular search patterns
        patterns = await self.get_popular_search_patterns()
        
        # Warm cache for each pattern
        successful_warms = 0
        failed_warms = 0
        
        for i, pattern in enumerate(patterns, 1):
            logger.info(f"🔥 Warming cache {i}/{len(patterns)}")
            
            try:
                success = await self.warm_cache_for_pattern(pattern)
                if success:
                    successful_warms += 1
                else:
                    failed_warms += 1
                
                # Add delay between requests to avoid overwhelming APIs
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"❌ Failed to warm cache for pattern {i}: {e}")
                failed_warms += 1
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        summary = {
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "duration_seconds": duration,
            "patterns_processed": len(patterns),
            "successful_warms": successful_warms,
            "failed_warms": failed_warms,
            "success_rate": (successful_warms / len(patterns) * 100) if patterns else 0
        }
        
        logger.info(f"🏁 Cache warming completed: {successful_warms}/{len(patterns)} successful ({summary['success_rate']:.1f}%)")
        
        return summary

async def main():
    """Main entry point for cache warming"""
    warmer = CacheWarmer()
    
    try:
        summary = await warmer.run_cache_warming()
        
        # Log summary to database for monitoring
        try:
            supabase.table("cache_warming_logs").insert({
                "run_date": datetime.now().date().isoformat(),
                "summary": summary,
                "created_at": datetime.now().isoformat()
            }).execute()
        except Exception as e:
            logger.error(f"Failed to log cache warming summary: {e}")
        
        print(f"✅ Cache warming completed successfully!")
        print(f"📊 Summary: {summary}")
        
    except Exception as e:
        logger.error(f"❌ Cache warming failed: {e}")
        print(f"❌ Cache warming failed: {e}")
        exit(1)

if __name__ == "__main__":
    asyncio.run(main())
