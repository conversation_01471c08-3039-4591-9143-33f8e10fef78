-- Fix missing INSERT policy for profiles table
-- This resolves the RLS timing issue where users cannot access their profile immediately after signup

-- Add INSERT policy for profiles table
CREATE POLICY "Users can insert their own profile"
  ON profiles FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Also add a policy to allow the trigger function to insert profiles
-- This ensures both manual and automatic profile creation work
CREATE POLICY "System can insert profiles"
  ON profiles FOR INSERT
  WITH CHECK (true);