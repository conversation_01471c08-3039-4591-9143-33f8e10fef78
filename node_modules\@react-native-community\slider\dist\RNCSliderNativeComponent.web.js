var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=void 0;var _toConsumableArray2=_interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));var _slicedToArray2=_interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));var _objectWithoutProperties2=_interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));var _react=_interopRequireWildcard(require("react"));var _reactNative=require("react-native");var _jsxRuntime=require("react/jsx-runtime");var _excluded=["value","minimumValue","maximumValue","lowerLimit","upperLimit","step","minimumTrackTintColor","maximumTrackTintColor","thumbTintColor","thumbStyle","style","inverted","disabled","trackHeight","thumbSize","thumbImage","onRNCSliderSlidingStart","onRNCSliderSlidingComplete","onRNCSliderValueChange"];var _this=this,_jsxFileName="/Users/<USER>/Desktop/Projects/react-native-slider/package/src/RNCSliderNativeComponent.web.tsx";function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap(),t=new WeakMap();return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?t:r;})(e);}function _interopRequireWildcard(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_getRequireWildcardCache(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&{}.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u];}return n.default=e,t&&t.set(e,n),n;}var valueToEvent=function valueToEvent(value){return{nativeEvent:{value:value}};};var RCTSliderWebComponent=_react.default.forwardRef(function(_ref,forwardedRef){var _ref$value=_ref.value,initialValue=_ref$value===void 0?0:_ref$value,_ref$minimumValue=_ref.minimumValue,minimumValue=_ref$minimumValue===void 0?0:_ref$minimumValue,_ref$maximumValue=_ref.maximumValue,maximumValue=_ref$maximumValue===void 0?0:_ref$maximumValue,_ref$lowerLimit=_ref.lowerLimit,lowerLimit=_ref$lowerLimit===void 0?0:_ref$lowerLimit,_ref$upperLimit=_ref.upperLimit,upperLimit=_ref$upperLimit===void 0?0:_ref$upperLimit,_ref$step=_ref.step,step=_ref$step===void 0?1:_ref$step,_ref$minimumTrackTint=_ref.minimumTrackTintColor,minimumTrackTintColor=_ref$minimumTrackTint===void 0?'#009688':_ref$minimumTrackTint,_ref$maximumTrackTint=_ref.maximumTrackTintColor,maximumTrackTintColor=_ref$maximumTrackTint===void 0?'#939393':_ref$maximumTrackTint,_ref$thumbTintColor=_ref.thumbTintColor,thumbTintColor=_ref$thumbTintColor===void 0?'#009688':_ref$thumbTintColor,_ref$thumbStyle=_ref.thumbStyle,thumbStyle=_ref$thumbStyle===void 0?{}:_ref$thumbStyle,_ref$style=_ref.style,style=_ref$style===void 0?{}:_ref$style,_ref$inverted=_ref.inverted,inverted=_ref$inverted===void 0?false:_ref$inverted,_ref$disabled=_ref.disabled,disabled=_ref$disabled===void 0?false:_ref$disabled,_ref$trackHeight=_ref.trackHeight,trackHeight=_ref$trackHeight===void 0?4:_ref$trackHeight,_ref$thumbSize=_ref.thumbSize,thumbSize=_ref$thumbSize===void 0?20:_ref$thumbSize,thumbImage=_ref.thumbImage,_ref$onRNCSliderSlidi=_ref.onRNCSliderSlidingStart,onRNCSliderSlidingStart=_ref$onRNCSliderSlidi===void 0?function(_){}:_ref$onRNCSliderSlidi,_ref$onRNCSliderSlidi2=_ref.onRNCSliderSlidingComplete,onRNCSliderSlidingComplete=_ref$onRNCSliderSlidi2===void 0?function(_){}:_ref$onRNCSliderSlidi2,_ref$onRNCSliderValue=_ref.onRNCSliderValueChange,onRNCSliderValueChange=_ref$onRNCSliderValue===void 0?function(_){}:_ref$onRNCSliderValue,others=(0,_objectWithoutProperties2.default)(_ref,_excluded);var containerSize=_react.default.useRef({width:0,height:0});var containerPositionX=_react.default.useRef(0);var containerRef=forwardedRef||_react.default.createRef();var containerPositionInvalidated=_react.default.useRef(false);var _React$useState=_react.default.useState(initialValue||minimumValue),_React$useState2=(0,_slicedToArray2.default)(_React$useState,2),value=_React$useState2[0],setValue=_React$useState2[1];var lastInitialValue=_react.default.useRef();var animationValues=_react.default.useRef({val:new _reactNative.Animated.Value(value),min:new _reactNative.Animated.Value(minimumValue),max:new _reactNative.Animated.Value(maximumValue),diff:new _reactNative.Animated.Value(maximumValue-minimumValue||1)}).current;_react.default.useEffect(function(){animationValues.min.setValue(minimumValue);animationValues.max.setValue(maximumValue);animationValues.diff.setValue(maximumValue-minimumValue||1);},[animationValues,minimumValue,maximumValue]);var minPercent=_react.default.useRef(_reactNative.Animated.multiply(new _reactNative.Animated.Value(100),_reactNative.Animated.divide(_reactNative.Animated.subtract(animationValues.val,animationValues.min),animationValues.diff))).current;var maxPercent=_react.default.useRef(_reactNative.Animated.subtract(new _reactNative.Animated.Value(100),minPercent)).current;var onValueChange=(0,_react.useCallback)(function(value){onRNCSliderValueChange&&onRNCSliderValueChange(valueToEvent(value));},[onRNCSliderValueChange]);var onSlidingStart=(0,_react.useCallback)(function(value){isUserInteracting.current=true;onRNCSliderSlidingStart&&onRNCSliderSlidingStart(valueToEvent(value));},[onRNCSliderSlidingStart]);var onSlidingComplete=(0,_react.useCallback)(function(value){isUserInteracting.current=false;onRNCSliderSlidingComplete&&onRNCSliderSlidingComplete(valueToEvent(value));},[onRNCSliderSlidingComplete]);var isUserInteracting=_react.default.useRef(false);var _updateValue=(0,_react.useCallback)(function(newValue){var hardRounded=decimalPrecision.current<20?Number.parseFloat(newValue.toFixed(decimalPrecision.current)):newValue;var withinBounds=Math.max(minimumValue,Math.min(hardRounded,maximumValue));if(value!==withinBounds){setValue(withinBounds);if(isUserInteracting.current){onValueChange(withinBounds);}return withinBounds;}return hardRounded;},[minimumValue,maximumValue,value,onValueChange]);_react.default.useLayoutEffect(function(){if(initialValue!==lastInitialValue.current){lastInitialValue.current=initialValue;var newValue=_updateValue(initialValue);animationValues.val.setValue(newValue);}},[initialValue,_updateValue,animationValues]);_react.default.useEffect(function(){var invalidateContainerPosition=function invalidateContainerPosition(){containerPositionInvalidated.current=true;};var onDocumentScroll=function onDocumentScroll(e){var isAlreadyInvalidated=!containerPositionInvalidated.current;if(isAlreadyInvalidated&&containerRef.current&&e.target.contains(containerRef.current)){invalidateContainerPosition();}};window.addEventListener('resize',invalidateContainerPosition);document.addEventListener('scroll',onDocumentScroll,{capture:true});return function(){window.removeEventListener('resize',invalidateContainerPosition);document.removeEventListener('scroll',onDocumentScroll,{capture:true});};},[containerRef]);var containerStyle=[{flexGrow:1,flexShrink:1,flexBasis:'auto',flexDirection:'row',alignItems:'center'},style];var trackStyle={height:trackHeight,borderRadius:trackHeight/2,userSelect:'none'};var minimumTrackStyle=Object.assign({},trackStyle,{backgroundColor:minimumTrackTintColor,flexGrow:minPercent});var maximumTrackStyle=Object.assign({},trackStyle,{backgroundColor:maximumTrackTintColor,flexGrow:maxPercent});var thumbViewStyle=[{width:thumbSize,height:thumbSize,backgroundColor:thumbTintColor,zIndex:1,borderRadius:thumbSize/2,overflow:'hidden'},thumbStyle];var decimalPrecision=_react.default.useRef(calculatePrecision(minimumValue,maximumValue,step));_react.default.useEffect(function(){decimalPrecision.current=calculatePrecision(minimumValue,maximumValue,step);},[maximumValue,minimumValue,step]);var updateContainerPositionX=function updateContainerPositionX(){var _current;var positionX=(_current=containerRef.current)==null?void 0:_current.getBoundingClientRect().x;containerPositionX.current=positionX!=null?positionX:0;};var getValueFromNativeEvent=function getValueFromNativeEvent(pageX){var adjustForThumbSize=(containerSize.current.width||1)>thumbSize;var width=(containerSize.current.width||1)-(adjustForThumbSize?thumbSize:0);if(containerPositionInvalidated.current){containerPositionInvalidated.current=false;updateContainerPositionX();}var containerX=containerPositionX.current+(adjustForThumbSize?thumbSize/2:0);var lowerValue=minimumValue<lowerLimit?lowerLimit:minimumValue;var upperValue=maximumValue>upperLimit?upperLimit:maximumValue;if(pageX<containerX){return inverted?upperValue:lowerValue;}else if(pageX>containerX+width){return inverted?lowerValue:upperValue;}else{var x=pageX-containerX;var newValue=inverted?maximumValue-(maximumValue-minimumValue)*x/width:minimumValue+(maximumValue-minimumValue)*x/width;var valueAfterStep=step?Math.round(newValue/step)*step:newValue;var valueAfterLowerLimit=valueAfterStep<lowerLimit?lowerLimit:valueAfterStep;var valueInLimitRange=valueAfterLowerLimit>upperLimit?upperLimit:valueAfterLowerLimit;return valueInLimitRange;}};var onTouchEnd=function onTouchEnd(_ref2){var nativeEvent=_ref2.nativeEvent;var newValue=_updateValue(getValueFromNativeEvent(nativeEvent.pageX));animationValues.val.setValue(newValue);onSlidingComplete(newValue);};var onMove=function onMove(_ref3){var nativeEvent=_ref3.nativeEvent;var newValue=getValueFromNativeEvent(nativeEvent.pageX);animationValues.val.setValue(newValue);_updateValue(newValue);};var accessibilityActions=function accessibilityActions(event){var tenth=(maximumValue-minimumValue)/10;switch(event.nativeEvent.actionName){case'increment':_updateValue(value+(step||tenth));break;case'decrement':_updateValue(value-(step||tenth));break;}};_react.default.useImperativeHandle(forwardedRef,function(){return{updateValue:function updateValue(val){_updateValue(val);}};},[_updateValue]);return(0,_jsxRuntime.jsxs)(_reactNative.View,Object.assign({ref:containerRef,onLayout:function onLayout(_ref4){var layout=_ref4.nativeEvent.layout;containerSize.current.height=layout.height;containerSize.current.width=layout.width;if(containerRef.current){updateContainerPositionX();}},accessibilityActions:[{name:'increment',label:'increment'},{name:'decrement',label:'decrement'}],onAccessibilityAction:accessibilityActions,accessible:true,accessibilityRole:'adjustable',style:containerStyle},others,{onStartShouldSetResponder:function onStartShouldSetResponder(){return!disabled;},onMoveShouldSetResponder:function onMoveShouldSetResponder(){return!disabled;},onResponderGrant:function onResponderGrant(){return onSlidingStart(value);},onResponderRelease:onTouchEnd,onResponderMove:onMove,children:[(0,_jsxRuntime.jsx)(_reactNative.Animated.View,{pointerEvents:"none",style:minimumTrackStyle}),(0,_jsxRuntime.jsx)(_reactNative.View,{pointerEvents:"none",style:thumbViewStyle,children:thumbImage!==undefined?(0,_jsxRuntime.jsx)(_reactNative.Image,{source:thumbImage,style:{width:'100%',height:'100%'}}):null}),(0,_jsxRuntime.jsx)(_reactNative.Animated.View,{pointerEvents:"none",style:maximumTrackStyle})]}));});function calculatePrecision(minimumValue,maximumValue,step){if(!step){return Infinity;}else{var decimals=[minimumValue,maximumValue,step].map(function(value){return((value+'').split('.').pop()||'').length;});return Math.max.apply(Math,(0,_toConsumableArray2.default)(decimals));}}RCTSliderWebComponent.displayName='RTCSliderWebComponent';var _default=exports.default=RCTSliderWebComponent;