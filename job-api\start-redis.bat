@echo off
echo Starting Redis server for development...
echo.

REM Check if Redis is installed
where redis-server >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Redis is not installed or not in PATH.
    echo.
    echo To install Redis on Windows:
    echo 1. Download Redis from: https://github.com/microsoftarchive/redis/releases
    echo 2. Or use WSL: wsl --install then sudo apt install redis-server
    echo 3. Or use Docker: docker run -d -p 6379:6379 redis:alpine
    echo.
    pause
    exit /b 1
)

echo Starting Redis server on port 6379...
redis-server --port 6379 --save 60 1 --loglevel notice

pause
