"""
API Usage Tracking and Quota Management
Prevents API overage charges and manages rate limits
"""

import asyncio
from datetime import datetime, date
from typing import Dict, Any, Callable, Awaitable
import logging
from supabase_client import supabase

logger = logging.getLogger(__name__)

class APILimitReached(Exception):
    """Raised when API quota is exhausted"""
    def __init__(self, api_name: str, quota_remaining: int = 0):
        self.api_name = api_name
        self.quota_remaining = quota_remaining
        super().__init__(f"API quota exhausted for {api_name} (remaining: {quota_remaining})")

class APIUsageTracker:
    def __init__(self):
        # Daily quotas for each API (free tier limits)
        self.daily_quotas = {
            'jooble': 1000,      # Jooble free tier
            'adzuna': 1000,      # Adzuna free tier  
            'muse': 500,         # The Muse rate limit
            'arbeitnow': 2000,   # Arbeitnow (usually unlimited but let's be safe)
            'remoteok': 1000     # RemoteOK backup
        }
        
        # Cost per API call (for monitoring)
        self.api_costs = {
            'jooble': 0.001,     # $0.001 per call
            'adzuna': 0.002,     # $0.002 per call
            'muse': 0.0,         # Free
            'arbeitnow': 0.0,    # Free
            'remoteok': 0.0      # Free
        }
    
    async def get_usage_record(self, api_name: str, date_str: str = None) -> Dict[str, Any]:
        """Get or create usage record for API and date"""
        if not date_str:
            date_str = date.today().isoformat()
        
        try:
            # Try to get existing record
            response = supabase.table("api_usage_tracking").select("*").eq("api_name", api_name).eq("date", date_str).execute()
            
            if response.data:
                return response.data[0]
            else:
                # Create new record
                new_record = {
                    "api_name": api_name,
                    "date": date_str,
                    "calls_made": 0,
                    "quota_remaining": self.daily_quotas.get(api_name, 1000),
                    "estimated_cost": 0.0,
                    "created_at": datetime.now().isoformat()
                }
                
                insert_response = supabase.table("api_usage_tracking").insert(new_record).execute()
                return insert_response.data[0] if insert_response.data else new_record
                
        except Exception as e:
            logger.error(f"Error getting usage record for {api_name}: {e}")
            # Return default record if database fails
            return {
                "api_name": api_name,
                "date": date_str,
                "calls_made": 0,
                "quota_remaining": self.daily_quotas.get(api_name, 1000),
                "estimated_cost": 0.0
            }
    
    async def update_usage_record(self, record: Dict[str, Any], calls_increment: int = 1) -> bool:
        """Update usage record after API call"""
        try:
            updated_record = {
                **record,
                "calls_made": record["calls_made"] + calls_increment,
                "quota_remaining": max(0, record["quota_remaining"] - calls_increment),
                "estimated_cost": record.get("estimated_cost", 0) + (calls_increment * self.api_costs.get(record["api_name"], 0)),
                "updated_at": datetime.now().isoformat()
            }
            
            if "id" in record:
                # Update existing record
                supabase.table("api_usage_tracking").update(updated_record).eq("id", record["id"]).execute()
            else:
                # Insert new record
                supabase.table("api_usage_tracking").insert(updated_record).execute()
            
            logger.info(f"📊 {record['api_name']}: {updated_record['calls_made']} calls, {updated_record['quota_remaining']} remaining")
            return True
            
        except Exception as e:
            logger.error(f"Error updating usage record: {e}")
            return False
    
    async def check_quota(self, api_name: str) -> bool:
        """Check if API has remaining quota"""
        record = await self.get_usage_record(api_name)
        return record["quota_remaining"] > 0
    
    async def get_all_usage_stats(self) -> Dict[str, Any]:
        """Get usage statistics for all APIs"""
        today = date.today().isoformat()
        stats = {}
        
        for api_name in self.daily_quotas.keys():
            record = await self.get_usage_record(api_name, today)
            stats[api_name] = {
                "calls_made": record["calls_made"],
                "quota_remaining": record["quota_remaining"],
                "quota_total": self.daily_quotas[api_name],
                "usage_percentage": (record["calls_made"] / self.daily_quotas[api_name]) * 100,
                "estimated_cost": record.get("estimated_cost", 0)
            }
        
        # Calculate totals
        total_calls = sum(stat["calls_made"] for stat in stats.values())
        total_cost = sum(stat["estimated_cost"] for stat in stats.values())
        
        return {
            "date": today,
            "apis": stats,
            "totals": {
                "calls_made": total_calls,
                "estimated_cost": round(total_cost, 4)
            }
        }

# Global tracker instance
usage_tracker = APIUsageTracker()

async def call_with_tracking(api_name: str, coro: Awaitable[Any], fallback_data: Any = None) -> Any:
    """
    Wrapper for API calls with usage tracking and quota enforcement
    
    Args:
        api_name: Name of the API being called
        coro: Async coroutine to execute
        fallback_data: Data to return if quota is exceeded
    
    Returns:
        API response data or fallback data
    
    Raises:
        APILimitReached: If quota is exhausted and no fallback provided
    """
    
    # Check quota before making call
    record = await usage_tracker.get_usage_record(api_name)
    
    if record["quota_remaining"] <= 0:
        logger.warning(f"🚫 {api_name} quota exhausted ({record['calls_made']} calls made today)")
        
        if fallback_data is not None:
            logger.info(f"🔄 Using fallback data for {api_name}")
            return fallback_data
        else:
            raise APILimitReached(api_name, record["quota_remaining"])
    
    try:
        # Make the API call
        logger.info(f"🔄 Calling {api_name} API (quota: {record['quota_remaining']} remaining)")
        result = await coro

        # Update usage on success
        await usage_tracker.update_usage_record(record, calls_increment=1)

        # Track in Redis cache for real-time monitoring
        from cache import track_api_usage
        await track_api_usage(api_name)

        # Record Prometheus metrics
        try:
            from metrics import record_api_call, update_api_quota
            record_api_call(api_name, "success")
            updated_record = await usage_tracker.get_usage_record(api_name)
            total_quota = usage_tracker.daily_quotas.get(api_name, 1000)
            update_api_quota(api_name, updated_record['quota_remaining'], total_quota)
        except ImportError:
            pass

        logger.info(f"✅ {api_name} API call successful")
        return result
        
    except APILimitReached:
        # Re-raise quota errors
        raise
        
    except Exception as e:
        # Log API errors but don't increment usage (failed calls don't count)
        logger.error(f"❌ {api_name} API call failed: {e}")

        # Track API errors
        from cache import cache
        today = datetime.now().strftime('%Y-%m-%d')
        await cache.incr(f"api_errors:{api_name}:{today}")

        # Record Prometheus metrics
        try:
            from metrics import record_api_error, record_api_call
            error_type = type(e).__name__
            record_api_error(api_name, error_type)
            record_api_call(api_name, "error")
        except ImportError:
            pass

        if fallback_data is not None:
            logger.info(f"🔄 Using fallback data for {api_name} after error")
            return fallback_data
        else:
            raise

async def get_api_health_status() -> Dict[str, Any]:
    """Get health status of all APIs"""
    health_status = {}
    
    for api_name in usage_tracker.daily_quotas.keys():
        record = await usage_tracker.get_usage_record(api_name)
        
        # Determine health status
        usage_pct = (record["calls_made"] / usage_tracker.daily_quotas[api_name]) * 100
        
        if record["quota_remaining"] <= 0:
            status = "exhausted"
        elif usage_pct >= 90:
            status = "critical"
        elif usage_pct >= 75:
            status = "warning"
        else:
            status = "healthy"
        
        health_status[api_name] = {
            "status": status,
            "calls_made": record["calls_made"],
            "quota_remaining": record["quota_remaining"],
            "usage_percentage": round(usage_pct, 1)
        }
    
    return health_status

async def reset_daily_quotas():
    """Reset quotas for new day (called by cron job)"""
    today = date.today().isoformat()
    
    for api_name, quota in usage_tracker.daily_quotas.items():
        try:
            # Create fresh record for today
            new_record = {
                "api_name": api_name,
                "date": today,
                "calls_made": 0,
                "quota_remaining": quota,
                "estimated_cost": 0.0,
                "created_at": datetime.now().isoformat()
            }
            
            supabase.table("api_usage_tracking").insert(new_record).execute()
            logger.info(f"🔄 Reset quota for {api_name}: {quota} calls")
            
        except Exception as e:
            logger.error(f"Error resetting quota for {api_name}: {e}")

# Convenience functions for specific APIs
async def call_jooble_with_tracking(coro: Awaitable[Any]) -> Any:
    """Call Jooble API with tracking"""
    return await call_with_tracking("jooble", coro, fallback_data=[])

async def call_adzuna_with_tracking(coro: Awaitable[Any]) -> Any:
    """Call Adzuna API with tracking"""
    return await call_with_tracking("adzuna", coro, fallback_data=[])

async def call_muse_with_tracking(coro: Awaitable[Any]) -> Any:
    """Call The Muse API with tracking"""
    return await call_with_tracking("muse", coro, fallback_data=[])

async def call_arbeitnow_with_tracking(coro: Awaitable[Any]) -> Any:
    """Call Arbeitnow API with tracking"""
    return await call_with_tracking("arbeitnow", coro, fallback_data=[])
