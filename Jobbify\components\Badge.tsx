import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';

interface BadgeProps {
  label: string;
  showClock?: boolean;
  style?: any;
  textStyle?: any;
}

export const Badge: React.FC<BadgeProps> = ({ 
  label, 
  showClock = false, 
  style,
  textStyle 
}) => {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;

  const getBadgeColor = (apiSource: string) => {
    switch (apiSource.toLowerCase()) {
      case 'jooble':
        return '#4CAF50';
      case 'adzuna':
        return '#2196F3';
      case 'muse':
        return '#9C27B0';
      case 'arbeitnow':
        return '#FF9800';
      case 'remoteok':
        return '#F44336';
      default:
        return themeColors.textSecondary;
    }
  };

  const badgeColor = getBadgeColor(label);

  return (
    <View 
      style={[
        styles.badge,
        { 
          backgroundColor: badgeColor + '20',
          borderColor: badgeColor + '40',
        },
        style
      ]}
    >
      {showClock && (
        <MaterialIcons 
          name="schedule" 
          size={12} 
          color={badgeColor}
          style={styles.clockIcon}
        />
      )}
      <Text 
        style={[
          styles.badgeText,
          { color: badgeColor },
          textStyle
        ]}
      >
        {label.toUpperCase()}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    opacity: 0.6,
  },
  clockIcon: {
    marginRight: 4,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
});
