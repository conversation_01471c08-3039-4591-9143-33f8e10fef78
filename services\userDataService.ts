import { supabase } from '../Jobbify/lib/supabase';

export interface UserApplication {
  id: string;
  user_id: string;
  job_id: string;
  job_title: string;
  company: string;
  location?: string;
  salary?: string;
  job_type?: string;
  description?: string;
  requirements?: string;
  benefits?: string;
  application_date: string;
  status: string;
  notes?: string;
  cover_letter?: string;
  resume_url?: string;
  created_at: string;
  updated_at: string;
  applied_at?: string;
  cover_letter_attached?: boolean;
  logo_url?: string;
  status_color?: string;
}

export interface UserMessage {
  id: string;
  user_id: string;
  sender_name: string;
  sender_avatar?: string;
  content: string;
  timestamp: string;
  is_read: boolean;
  message_type: string;
  created_at: string;
  updated_at: string;
}

export interface QuickJob {
  id: string;
  user_id: string;
  title: string;
  company: string;
  location?: string;
  pay?: string;
  description?: string;
  category?: string;
  urgency: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface UserPreferences {
  id: string;
  user_id: string;
  theme: string;
  notifications_enabled: boolean;
  email_notifications: boolean;
  push_notifications: boolean;
  job_alerts: boolean;
  preferred_job_types?: string[];
  preferred_locations?: string[];
  salary_range?: any;
  created_at: string;
  updated_at: string;
}

export interface SwipedJob {
  id: string;
  user_id: string;
  job_id: string;
  swipe_direction: 'left' | 'right';
  swiped_at: string;
  created_at: string;
}

class UserDataService {
  // User Applications
  async getUserApplications(userId: string): Promise<UserApplication[]> {
    try {
      const { data, error } = await supabase
        .from('user_applications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching user applications:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getUserApplications:', error);
      return [];
    }
  }

  async addUserApplication(application: Omit<UserApplication, 'id' | 'created_at' | 'updated_at'>): Promise<UserApplication | null> {
    try {
      const { data, error } = await supabase
        .from('user_applications')
        .insert([application])
        .select()
        .single();

      if (error) {
        console.error('Error adding user application:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in addUserApplication:', error);
      return null;
    }
  }

  async updateUserApplication(id: string, updates: Partial<UserApplication>): Promise<UserApplication | null> {
    try {
      const { data, error } = await supabase
        .from('user_applications')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating user application:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in updateUserApplication:', error);
      return null;
    }
  }

  async deleteUserApplication(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_applications')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting user application:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in deleteUserApplication:', error);
      return false;
    }
  }

  // User Messages
  async getUserMessages(userId: string): Promise<UserMessage[]> {
    try {
      const { data, error } = await supabase
        .from('user_messages')
        .select('*')
        .eq('user_id', userId)
        .order('timestamp', { ascending: false });

      if (error) {
        console.error('Error fetching user messages:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getUserMessages:', error);
      return [];
    }
  }

  async addUserMessage(message: Omit<UserMessage, 'id' | 'created_at' | 'updated_at'>): Promise<UserMessage | null> {
    try {
      const { data, error } = await supabase
        .from('user_messages')
        .insert([message])
        .select()
        .single();

      if (error) {
        console.error('Error adding user message:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in addUserMessage:', error);
      return null;
    }
  }

  async markMessageAsRead(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_messages')
        .update({ is_read: true, updated_at: new Date().toISOString() })
        .eq('id', id);

      if (error) {
        console.error('Error marking message as read:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in markMessageAsRead:', error);
      return false;
    }
  }

  // Quick Jobs
  async getQuickJobs(userId?: string): Promise<QuickJob[]> {
    try {
      let query = supabase
        .from('quick_jobs')
        .select('*')
        .order('created_at', { ascending: false });

      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching quick jobs:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getQuickJobs:', error);
      return [];
    }
  }

  async addQuickJob(job: Omit<QuickJob, 'id' | 'created_at' | 'updated_at'>): Promise<QuickJob | null> {
    try {
      const { data, error } = await supabase
        .from('quick_jobs')
        .insert([job])
        .select()
        .single();

      if (error) {
        console.error('Error adding quick job:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in addQuickJob:', error);
      return null;
    }
  }

  // User Preferences
  async getUserPreferences(userId: string): Promise<UserPreferences | null> {
    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
        console.error('Error fetching user preferences:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getUserPreferences:', error);
      return null;
    }
  }

  async saveUserPreferences(preferences: Omit<UserPreferences, 'id' | 'created_at' | 'updated_at'>): Promise<UserPreferences | null> {
    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .upsert([preferences], { onConflict: 'user_id' })
        .select()
        .single();

      if (error) {
        console.error('Error saving user preferences:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in saveUserPreferences:', error);
      return null;
    }
  }

  // Swiped Jobs
  async getSwipedJobs(userId: string): Promise<SwipedJob[]> {
    try {
      const { data, error } = await supabase
        .from('swiped_jobs')
        .select('*')
        .eq('user_id', userId)
        .order('swiped_at', { ascending: false });

      if (error) {
        console.error('Error fetching swiped jobs:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getSwipedJobs:', error);
      return [];
    }
  }

  async addSwipedJob(swipe: Omit<SwipedJob, 'id' | 'created_at'>): Promise<SwipedJob | null> {
    try {
      const { data, error } = await supabase
        .from('swiped_jobs')
        .upsert([swipe], { onConflict: 'user_id,job_id' })
        .select()
        .single();

      if (error) {
        console.error('Error adding swiped job:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in addSwipedJob:', error);
      return null;
    }
  }

  // Bookmarks (assuming this table already exists)
  async getBookmarks(userId: string): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('bookmarks')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching bookmarks:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getBookmarks:', error);
      return [];
    }
  }

  async addBookmark(bookmark: any): Promise<any | null> {
    try {
      const { data, error } = await supabase
        .from('bookmarks')
        .insert([bookmark])
        .select()
        .single();

      if (error) {
        console.error('Error adding bookmark:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in addBookmark:', error);
      return null;
    }
  }

  async removeBookmark(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('bookmarks')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error removing bookmark:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in removeBookmark:', error);
      return false;
    }
  }
}

export const userDataService = new UserDataService();