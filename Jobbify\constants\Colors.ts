/**
 * Colors.ts - Central color definitions for the application
 * 
 * This file synchronizes with Theme.ts to ensure consistent theming across the app.
 * These colors are used by the Themed components through useThemeColor.
 */
import { LightTheme, DarkTheme } from './Theme';

// Jobbify brand colors (base palette)
const jobbifyBlue = '#0078E7'; // Primary blue from the logo
const jobbifyLightBlue = '#4DA3FF'; // Lighter blue for accents
const jobbifyDarkBlue = '#005BB8'; // Darker blue for hover states
const jobbifyBlack = '#1A1A1A'; // Dark black for text
const jobbifyDarkGray = '#333333'; // Dark gray for secondary text
const jobbifyMediumGray = '#666666'; // Medium gray for tertiary text
const jobbifyLightGray = '#EEEEEE'; // Light gray for borders
const jobbifyWhite = '#FFFFFF'; // White

// Use the same colors as defined in Theme.ts for consistency
export default {
  light: {
    text: LightTheme.text,
    background: LightTheme.background,
    tint: LightTheme.tint,
    card: LightTheme.card,
    border: LightTheme.border,
    tabIconDefault: LightTheme.tabIconDefault,
    tabIconSelected: LightTheme.tabIconSelected,
    // Add more colors as needed for themed components
  },
  dark: {
    text: DarkTheme.text, 
    background: DarkTheme.background,
    tint: DarkTheme.tint,
    card: DarkTheme.card,
    border: DarkTheme.border,
    tabIconDefault: DarkTheme.tabIconDefault,
    tabIconSelected: DarkTheme.tabIconSelected,
    // Add more colors as needed for themed components
  },
};
