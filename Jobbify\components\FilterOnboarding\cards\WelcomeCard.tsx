import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { FontAwesome } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';

const { width } = Dimensions.get('window');

interface WelcomeCardProps {
  onNext: () => void;
  theme: string;
  themeColors: any;
}

export default function WelcomeCard({ onNext, theme, themeColors }: WelcomeCardProps) {
  const handleNext = () => {
    try {
      console.log('[WELCOME_CARD] User clicked Get Started');
      onNext();
    } catch (error) {
      console.error('[WELCOME_CARD] Error in handleNext:', error);
      // Still try to proceed
      onNext();
    }
  };

  return (
    <View style={styles.container}>
      <Animated.View entering={FadeInUp.delay(200)} style={styles.iconContainer}>
        <LinearGradient
          colors={['#667eea', '#764ba2']}
          style={styles.iconGradient}
        >
          <FontAwesome name="magic" size={40} color="#FFFFFF" />
        </LinearGradient>
      </Animated.View>

      <Animated.View entering={FadeInUp.delay(400)} style={styles.content}>
        <Text style={[styles.title, { color: themeColors.text }]}>
          Let's Find Your Perfect Job
        </Text>
        <Text style={[styles.subtitle, { color: themeColors.textSecondary }]}>
          We'll ask you a few quick questions to personalize your job search and show you the most relevant opportunities.
        </Text>
      </Animated.View>

      <Animated.View entering={FadeInUp.delay(600)} style={styles.features}>
        <View style={styles.feature}>
          <FontAwesome name="bullseye" size={20} color={themeColors.tint} />
          <Text style={[styles.featureText, { color: themeColors.text }]}>
            Personalized job matching
          </Text>
        </View>
        <View style={styles.feature}>
          <FontAwesome name="filter" size={20} color={themeColors.tint} />
          <Text style={[styles.featureText, { color: themeColors.text }]}>
            Smart filtering system
          </Text>
        </View>
        <View style={styles.feature}>
          <FontAwesome name="heart" size={20} color={themeColors.tint} />
          <Text style={[styles.featureText, { color: themeColors.text }]}>
            Save time with relevant results
          </Text>
        </View>
      </Animated.View>

      <Animated.View entering={FadeInDown.delay(800)} style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, { backgroundColor: themeColors.tint }]}
          onPress={handleNext}
        >
          <Text style={styles.buttonText}>Get Started</Text>
          <FontAwesome name="arrow-right" size={16} color="#FFFFFF" />
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  iconContainer: {
    marginBottom: 32,
  },
  iconGradient: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    marginBottom: 48,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    paddingHorizontal: 16,
  },
  features: {
    alignItems: 'flex-start',
    marginBottom: 48,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 12,
  },
  featureText: {
    fontSize: 16,
    fontWeight: '500',
  },
  buttonContainer: {
    width: '100%',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    gap: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
});
