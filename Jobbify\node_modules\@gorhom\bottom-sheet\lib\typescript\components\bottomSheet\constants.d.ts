declare const DEFAULT_HANDLE_HEIGHT = 24;
declare const DEFAULT_OVER_DRAG_RESISTANCE_FACTOR = 2.5;
declare const DEFAULT_ENABLE_CONTENT_PANNING_GESTURE = true;
declare const DEFAULT_ENABLE_HANDLE_PANNING_GESTURE = true;
declare const DEFAULT_ENABLE_OVER_DRAG = true;
declare const DEFAULT_ENABLE_PAN_DOWN_TO_CLOSE = false;
declare const DEFAULT_ANIMATE_ON_MOUNT = true;
declare const DEFAULT_DYNAMIC_SIZING = true;
declare const DEFAULT_KEYBOARD_BEHAVIOR: "interactive";
declare const DEFAULT_KEYBOARD_BLUR_BEHAVIOR: "none";
declare const DEFAULT_KEYBOARD_INPUT_MODE: "adjustPan";
declare const DEFAULT_ENABLE_BLUR_KEYBOARD_ON_GESTURE = false;
declare const INITIAL_VALUE: number;
declare const INITIAL_SNAP_POINT = -999;
declare const INITIAL_CONTAINER_HEIGHT = -999;
declare const INITIAL_CONTAINER_OFFSET: {
    top: number;
    bottom: number;
    left: number;
    right: number;
};
declare const INITIAL_HANDLE_HEIGHT = -999;
declare const INITIAL_POSITION: number;
declare const DEFAULT_ACCESSIBLE = true;
declare const DEFAULT_ACCESSIBILITY_LABEL = "Bottom Sheet";
declare const DEFAULT_ACCESSIBILITY_ROLE = "adjustable";
export { DEFAULT_HANDLE_HEIGHT, DEFAULT_OVER_DRAG_RESISTANCE_FACTOR, DEFAULT_ENABLE_CONTENT_PANNING_GESTURE, DEFAULT_ENABLE_HANDLE_PANNING_GESTURE, DEFAULT_ENABLE_OVER_DRAG, DEFAULT_ENABLE_PAN_DOWN_TO_CLOSE, DEFAULT_DYNAMIC_SIZING, DEFAULT_ANIMATE_ON_MOUNT, DEFAULT_KEYBOARD_BEHAVIOR, DEFAULT_KEYBOARD_BLUR_BEHAVIOR, DEFAULT_KEYBOARD_INPUT_MODE, DEFAULT_ENABLE_BLUR_KEYBOARD_ON_GESTURE, INITIAL_POSITION, INITIAL_CONTAINER_HEIGHT, INITIAL_CONTAINER_OFFSET, INITIAL_HANDLE_HEIGHT, INITIAL_SNAP_POINT, INITIAL_VALUE, DEFAULT_ACCESSIBLE, DEFAULT_ACCESSIBILITY_LABEL, DEFAULT_ACCESSIBILITY_ROLE, };
//# sourceMappingURL=constants.d.ts.map