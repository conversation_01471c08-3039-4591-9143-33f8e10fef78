import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { JobEvaluation } from '@/services/jobEvaluationService';

interface AIEvaluationSummaryProps {
  evaluation: JobEvaluation;
  themeColors: {
    background: string;
    card: string;
    text: string;
    textSecondary: string;
    tint: string;
  };
  compact?: boolean;
  style?: any;
}

export const AIEvaluationSummary: React.FC<AIEvaluationSummaryProps> = ({ 
  evaluation, 
  themeColors, 
  compact = false,
  style 
}) => {
  const getScoreColor = (score: number): string => {
    if (score >= 8) return '#4CAF50'; // Green - Excellent
    if (score >= 6) return '#2196F3'; // Blue - Good
    if (score >= 4) return '#FF9800'; // Orange - Fair
    return '#F44336'; // Red - Poor
  };

  const getScoreEmoji = (score: number): string => {
    if (score >= 8) return '🌟';
    if (score >= 6) return '⭐';
    if (score >= 4) return '✨';
    return '⚠️';
  };

  const getComplexityEmoji = (complexity: string): string => {
    const emojis = {
      'entry': '🌱',
      'mid': '🚀', 
      'senior': '👑',
      'executive': '💎'
    };
    return emojis[complexity as keyof typeof emojis] || '🚀';
  };

  const scoreColor = getScoreColor(evaluation.overallScore);
  const scoreEmoji = getScoreEmoji(evaluation.overallScore);
  const complexityEmoji = getComplexityEmoji(evaluation.roleComplexity);

  if (compact) {
    return (
      <View style={[styles.compactContainer, { backgroundColor: themeColors.card }, style]}>
        <View style={styles.compactHeader}>
          <MaterialIcons name="psychology" size={16} color={themeColors.tint} />
          <Text style={[styles.compactTitle, { color: themeColors.text }]}>AI Analysis</Text>
          <Text style={styles.compactEmoji}>{scoreEmoji}</Text>
          <Text style={[styles.compactScore, { color: scoreColor }]}>
            {evaluation.overallScore}/10
          </Text>
        </View>
        <View style={styles.compactStats}>
          <View style={styles.compactStat}>
            <Text style={styles.compactStatEmoji}>{complexityEmoji}</Text>
            <Text style={[styles.compactStatText, { color: themeColors.textSecondary }]}>
              {evaluation.roleComplexity.charAt(0).toUpperCase() + evaluation.roleComplexity.slice(1)}
            </Text>
          </View>
          <View style={styles.compactStat}>
            <Text style={styles.compactStatEmoji}>💰</Text>
            <Text style={[styles.compactStatText, { color: themeColors.textSecondary }]}>
              {evaluation.salaryEstimate.split(' ').slice(0, 2).join(' ')}
            </Text>
          </View>
          <View style={styles.compactStat}>
            <Text style={styles.compactStatEmoji}>🌐</Text>
            <Text style={[styles.compactStatText, { color: themeColors.textSecondary }]}>
              {evaluation.remoteWorkFriendly.includes('remote') || evaluation.remoteWorkFriendly.includes('Remote') ? 'Remote' : 'Office'}
            </Text>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: themeColors.card }, style]}>
      <View style={styles.header}>
        <View style={styles.titleRow}>
          <MaterialIcons name="psychology" size={18} color={themeColors.tint} />
          <Text style={[styles.title, { color: themeColors.text }]}>AI Job Analysis</Text>
          <Text style={styles.emoji}>{scoreEmoji}</Text>
        </View>
        <Text style={[styles.score, { color: scoreColor }]}>
          {evaluation.overallScore}/10
        </Text>
      </View>
      
      <Text style={[styles.summary, { color: themeColors.textSecondary }]}>
        {evaluation.aiSummary}
      </Text>

      <View style={styles.statsRow}>
        <View style={styles.stat}>
          <Text style={styles.statEmoji}>{complexityEmoji}</Text>
          <Text style={[styles.statLabel, { color: themeColors.textSecondary }]}>Level</Text>
          <Text style={[styles.statValue, { color: themeColors.text }]}>
            {evaluation.roleComplexity.charAt(0).toUpperCase() + evaluation.roleComplexity.slice(1)}
          </Text>
        </View>
        
        <View style={styles.stat}>
          <Text style={styles.statEmoji}>💰</Text>
          <Text style={[styles.statLabel, { color: themeColors.textSecondary }]}>Salary</Text>
          <Text style={[styles.statValue, { color: themeColors.text }]}>
            {evaluation.salaryEstimate.split(' ').slice(0, 2).join(' ')}
          </Text>
        </View>
        
        <View style={styles.stat}>
          <Text style={styles.statEmoji}>🌐</Text>
          <Text style={[styles.statLabel, { color: themeColors.textSecondary }]}>Work Style</Text>
          <Text style={[styles.statValue, { color: themeColors.text }]}>
            {evaluation.remoteWorkFriendly.includes('remote') || evaluation.remoteWorkFriendly.includes('Remote') ? 'Remote OK' : 'Office'}
          </Text>
        </View>
      </View>

      {/* Quick highlights */}
      <View style={styles.highlights}>
        {evaluation.strengths.slice(0, 2).map((strength, index) => (
          <View key={index} style={styles.highlight}>
            <Text style={[styles.highlightBullet, { color: '#4CAF50' }]}>✓</Text>
            <Text style={[styles.highlightText, { color: themeColors.textSecondary }]}>
              {strength}
            </Text>
          </View>
        ))}
        {evaluation.concerns.slice(0, 1).map((concern, index) => (
          <View key={`concern-${index}`} style={styles.highlight}>
            <Text style={[styles.highlightBullet, { color: '#FF9800' }]}>⚠</Text>
            <Text style={[styles.highlightText, { color: themeColors.textSecondary }]}>
              {concern}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 12,
    borderRadius: 8,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
    flex: 1,
  },
  emoji: {
    fontSize: 14,
    marginLeft: 4,
  },
  score: {
    fontSize: 14,
    fontWeight: '700',
  },
  summary: {
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 8,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  stat: {
    alignItems: 'center',
    flex: 1,
  },
  statEmoji: {
    fontSize: 14,
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 10,
    marginBottom: 1,
  },
  statValue: {
    fontSize: 11,
    fontWeight: '500',
    textAlign: 'center',
  },
  highlights: {
    gap: 4,
  },
  highlight: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  highlightBullet: {
    fontSize: 12,
    marginRight: 6,
    marginTop: 1,
  },
  highlightText: {
    fontSize: 11,
    flex: 1,
    lineHeight: 14,
  },
  // Compact styles
  compactContainer: {
    padding: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  compactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  compactTitle: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    flex: 1,
  },
  compactEmoji: {
    fontSize: 12,
    marginRight: 4,
  },
  compactScore: {
    fontSize: 12,
    fontWeight: '700',
  },
  compactStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  compactStat: {
    alignItems: 'center',
    flex: 1,
  },
  compactStatEmoji: {
    fontSize: 12,
    marginBottom: 2,
  },
  compactStatText: {
    fontSize: 10,
    textAlign: 'center',
  },
});
