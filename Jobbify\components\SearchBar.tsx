import React, { useState, useRef } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Keyboard,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';

interface SearchBarProps {
  onSearch: (query: string) => void;
  placeholder?: string;
  loading?: boolean;
  autoFocus?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  onClear?: () => void;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  onSearch,
  placeholder = "Search jobs...",
  loading = false,
  autoFocus = false,
  onFocus,
  onBlur,
  onClear
}) => {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<TextInput>(null);
  const focusAnim = useRef(new Animated.Value(0)).current;

  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
    
    Animated.timing(focusAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: false,
    }).start();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
    
    Animated.timing(focusAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  };

  const handleSearch = () => {
    if (query.trim()) {
      onSearch(query.trim());
      Keyboard.dismiss();
    }
  };

  const handleClear = () => {
    setQuery('');
    onClear?.();
    inputRef.current?.focus();
  };

  const borderColor = focusAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [themeColors.border, themeColors.tint],
  });

  const backgroundColor = focusAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [themeColors.card, themeColors.background],
  });

  return (
    <Animated.View 
      style={[
        styles.container,
        {
          backgroundColor,
          borderColor,
        }
      ]}
    >
      <MaterialIcons 
        name="search" 
        size={24} 
        color={isFocused ? themeColors.tint : themeColors.textSecondary}
        style={styles.searchIcon}
      />
      
      <TextInput
        ref={inputRef}
        style={[
          styles.input,
          { 
            color: themeColors.text,
            flex: 1,
          }
        ]}
        placeholder={placeholder}
        placeholderTextColor={themeColors.textSecondary}
        value={query}
        onChangeText={setQuery}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onSubmitEditing={handleSearch}
        returnKeyType="search"
        autoFocus={autoFocus}
        autoCorrect={false}
        autoCapitalize="none"
      />

      {loading && (
        <ActivityIndicator 
          size="small" 
          color={themeColors.tint}
          style={styles.loadingIcon}
        />
      )}

      {query.length > 0 && !loading && (
        <TouchableOpacity
          onPress={handleClear}
          style={styles.clearButton}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <MaterialIcons 
            name="clear" 
            size={20} 
            color={themeColors.textSecondary}
          />
        </TouchableOpacity>
      )}

      {query.length > 0 && !loading && (
        <TouchableOpacity
          onPress={handleSearch}
          style={[styles.searchButton, { backgroundColor: themeColors.tint }]}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <MaterialIcons 
            name="arrow-forward" 
            size={20} 
            color="#FFFFFF"
          />
        </TouchableOpacity>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 48,
    borderRadius: 24,
    borderWidth: 1,
    paddingHorizontal: 16,
    marginHorizontal: 16,
    marginVertical: 8,
  },
  searchIcon: {
    marginRight: 12,
  },
  input: {
    fontSize: 16,
    paddingVertical: 0,
  },
  loadingIcon: {
    marginLeft: 8,
  },
  clearButton: {
    marginLeft: 8,
    padding: 4,
  },
  searchButton: {
    marginLeft: 8,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
