import React from 'react';
import { type SimultaneousGesture } from 'react-native-gesture-handler';
interface BottomSheetDraggableScrollableProps {
    scrollableGesture?: SimultaneousGesture;
    children: React.ReactNode;
}
export declare function BottomSheetDraggableScrollable({ scrollableGesture, children, }: BottomSheetDraggableScrollableProps): string | number | boolean | Iterable<React.ReactNode> | React.JSX.Element | null | undefined;
export {};
//# sourceMappingURL=BottomSheetDraggableScrollable.d.ts.map