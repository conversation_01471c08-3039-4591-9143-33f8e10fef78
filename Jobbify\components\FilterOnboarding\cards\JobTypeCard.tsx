import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';

interface JobTypeCardProps {
  preferences: any;
  onNext: (data: any) => void;
  onPrevious?: () => void;
  theme: string;
  themeColors: any;
}

const JOB_TYPES = [
  {
    id: 'full-time',
    title: 'Full-Time',
    subtitle: '40+ hours per week',
    icon: 'briefcase',
    color: '#3B82F6'
  },
  {
    id: 'part-time',
    title: 'Part-Time',
    subtitle: 'Less than 40 hours per week',
    icon: 'clock-o',
    color: '#10B981'
  },
  {
    id: 'contract',
    title: 'Contract',
    subtitle: 'Project-based work',
    icon: 'file-text-o',
    color: '#F59E0B'
  },
  {
    id: 'freelance',
    title: 'Freelance',
    subtitle: 'Independent contractor',
    icon: 'user',
    color: '#8B5CF6'
  },
  {
    id: 'internship',
    title: 'Internship',
    subtitle: 'Learning opportunity',
    icon: 'graduation-cap',
    color: '#EF4444'
  },
  {
    id: 'temporary',
    title: 'Temporary',
    subtitle: 'Short-term position',
    icon: 'calendar',
    color: '#F97316'
  }
];

export default function JobTypeCard({ 
  preferences, 
  onNext, 
  onPrevious, 
  theme, 
  themeColors 
}: JobTypeCardProps) {
  const [selectedJobTypes, setSelectedJobTypes] = useState<string[]>(preferences.jobTypes || []);

  const toggleJobType = (jobTypeId: string) => {
    setSelectedJobTypes(prev => {
      if (prev.includes(jobTypeId)) {
        return prev.filter(id => id !== jobTypeId);
      } else {
        return [...prev, jobTypeId];
      }
    });
  };

  const handleNext = () => {
    if (selectedJobTypes.length === 0) {
      Alert.alert('Job Type Required', 'Please select at least one job type.');
      return;
    }
    onNext({ jobTypes: selectedJobTypes });
  };

  const renderJobTypeOption = (jobType: typeof JOB_TYPES[0]) => {
    const isSelected = selectedJobTypes.includes(jobType.id);
    
    return (
      <TouchableOpacity
        key={jobType.id}
        style={[
          styles.jobTypeCard,
          {
            backgroundColor: isSelected ? jobType.color : themeColors.card,
            borderColor: isSelected ? jobType.color : themeColors.border,
          }
        ]}
        onPress={() => toggleJobType(jobType.id)}
      >
        <View style={[
          styles.iconContainer,
          { backgroundColor: isSelected ? 'rgba(255,255,255,0.2)' : jobType.color + '20' }
        ]}>
          <FontAwesome 
            name={jobType.icon as any} 
            size={20} 
            color={isSelected ? '#FFFFFF' : jobType.color} 
          />
        </View>
        
        <View style={styles.textContainer}>
          <Text style={[
            styles.jobTypeTitle,
            { color: isSelected ? '#FFFFFF' : themeColors.text }
          ]}>
            {jobType.title}
          </Text>
          <Text style={[
            styles.jobTypeSubtitle,
            { color: isSelected ? 'rgba(255,255,255,0.8)' : themeColors.textSecondary }
          ]}>
            {jobType.subtitle}
          </Text>
        </View>

        <View style={[
          styles.checkbox,
          {
            backgroundColor: isSelected ? '#FFFFFF' : 'transparent',
            borderColor: isSelected ? '#FFFFFF' : themeColors.border,
          }
        ]}>
          {isSelected && (
            <FontAwesome name="check" size={12} color={jobType.color} />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <Animated.View entering={FadeInUp.delay(200)} style={styles.header}>
        <Text style={[styles.title, { color: themeColors.text }]}>
          What type of work are you looking for?
        </Text>
        <Text style={[styles.subtitle, { color: themeColors.textSecondary }]}>
          Select all that apply
        </Text>
      </Animated.View>

      <Animated.View entering={FadeInUp.delay(400)} style={styles.content}>
        <ScrollView 
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {JOB_TYPES.map(renderJobTypeOption)}
        </ScrollView>
        
        {selectedJobTypes.length > 0 && (
          <Animated.View entering={FadeInUp.delay(200)} style={styles.selectedContainer}>
            <Text style={[styles.selectedText, { color: themeColors.textSecondary }]}>
              {selectedJobTypes.length} type{selectedJobTypes.length > 1 ? 's' : ''} selected
            </Text>
          </Animated.View>
        )}
      </Animated.View>

      <Animated.View entering={FadeInDown.delay(600)} style={styles.buttonContainer}>
        <View style={styles.buttonRow}>
          {onPrevious && (
            <TouchableOpacity
              style={[styles.backButton, { borderColor: themeColors.border }]}
              onPress={onPrevious}
            >
              <FontAwesome name="arrow-left" size={16} color={themeColors.textSecondary} />
              <Text style={[styles.backText, { color: themeColors.textSecondary }]}>
                Back
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[
              styles.nextButton,
              { 
                backgroundColor: selectedJobTypes.length > 0 ? themeColors.tint : themeColors.border,
                opacity: selectedJobTypes.length > 0 ? 1 : 0.5
              },
              !onPrevious && styles.fullWidthButton
            ]}
            onPress={handleNext}
            disabled={selectedJobTypes.length === 0}
          >
            <Text style={styles.nextText}>Continue</Text>
            <FontAwesome name="arrow-right" size={16} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
    paddingTop: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    gap: 12,
    paddingBottom: 16,
  },
  jobTypeCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 2,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  jobTypeTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  jobTypeSubtitle: {
    fontSize: 13,
    lineHeight: 18,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  selectedContainer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  selectedText: {
    fontSize: 14,
    fontWeight: '500',
  },
  buttonContainer: {
    paddingBottom: 32,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 2,
    gap: 8,
    flex: 1,
  },
  backText: {
    fontSize: 16,
    fontWeight: '600',
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
    flex: 2,
  },
  fullWidthButton: {
    flex: 1,
  },
  nextText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
