import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  SafeAreaView,
  Switch,
  FlatList,
} from 'react-native';
import { router } from 'expo-router';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import Slider from '@react-native-community/slider';
import { supabase } from '@/lib/supabase';

// Location autocomplete data
const POPULAR_LOCATIONS = [
  'New York, NY', 'Los Angeles, CA', 'Chicago, IL', 'Houston, TX', 'Phoenix, AZ',
  'Philadelphia, PA', 'San Antonio, TX', 'San Diego, CA', 'Dallas, TX', 'San Jose, CA',
  'Austin, TX', 'Jacksonville, FL', 'Fort Worth, TX', 'Columbus, OH', 'Charlotte, NC',
  'San Francisco, CA', 'Indianapolis, IN', 'Seattle, WA', 'Denver, CO', 'Washington, DC',
  'Boston, MA', 'El Paso, TX', 'Nashville, TN', 'Detroit, MI', 'Oklahoma City, OK',
  'Portland, OR', 'Las Vegas, NV', 'Memphis, TN', 'Louisville, KY', 'Baltimore, MD',
  'Remote'
];

// Industry options
const INDUSTRY_OPTIONS = [
  'Technology', 'Healthcare', 'Finance', 'Education', 'Retail', 'Manufacturing',
  'Construction', 'Transportation', 'Real Estate', 'Media & Entertainment',
  'Government', 'Non-Profit', 'Consulting', 'Energy', 'Telecommunications',
  'Automotive', 'Aerospace', 'Biotechnology', 'Food & Beverage', 'Hospitality'
];

// Job types
const JOB_TYPES = ['Full-time', 'Part-time', 'Contract', 'Freelance', 'Internship', 'Remote'];

// Experience levels
const EXPERIENCE_LEVELS = [
  { key: 'entry', label: 'Entry Level (0-2 years)' },
  { key: 'junior', label: 'Junior (2-4 years)' },
  { key: 'mid', label: 'Mid Level (4-7 years)' },
  { key: 'senior', label: 'Senior (7+ years)' },
  { key: 'lead', label: 'Lead/Manager' },
  { key: 'executive', label: 'Executive' }
];

export default function QuickPreferencesOnboarding() {
  const { theme, user } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  // Form state
  const [locationInput, setLocationInput] = useState('');
  const [locationSuggestions, setLocationSuggestions] = useState<string[]>([]);
  const [showLocationSuggestions, setShowLocationSuggestions] = useState(false);
  const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
  const [selectedJobTypes, setSelectedJobTypes] = useState<string[]>([]);
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([]);
  const [experienceLevel, setExperienceLevel] = useState('mid');
  const [minSalary, setMinSalary] = useState(60000);
  const [maxSalary, setMaxSalary] = useState(120000);
  const [remotePreference, setRemotePreference] = useState('acceptable');

  // Location autocomplete functionality
  const handleLocationInputChange = (text: string) => {
    setLocationInput(text);
    if (text.length > 1) {
      const filtered = POPULAR_LOCATIONS.filter(location =>
        location.toLowerCase().includes(text.toLowerCase())
      ).slice(0, 5);
      setLocationSuggestions(filtered);
      setShowLocationSuggestions(filtered.length > 0);
    } else {
      setShowLocationSuggestions(false);
    }
  };

  const selectLocation = (location: string) => {
    if (!selectedLocations.includes(location)) {
      setSelectedLocations([...selectedLocations, location]);
    }
    setLocationInput('');
    setShowLocationSuggestions(false);
  };

  const removeLocation = (index: number) => {
    setSelectedLocations(selectedLocations.filter((_, i) => i !== index));
  };

  const toggleJobType = (jobType: string) => {
    setSelectedJobTypes(prev =>
      prev.includes(jobType)
        ? prev.filter(type => type !== jobType)
        : [...prev, jobType]
    );
  };

  const toggleIndustry = (industry: string) => {
    setSelectedIndustries(prev =>
      prev.includes(industry)
        ? prev.filter(ind => ind !== industry)
        : [...prev, industry]
    );
  };

  const handleSavePreferences = async () => {
    if (!user?.id) {
      setError('User not found. Please log in again.');
      return;
    }

    // Validation
    if (selectedLocations.length === 0) {
      setError('Please select at least one location.');
      return;
    }

    if (selectedJobTypes.length === 0) {
      setError('Please select at least one job type.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Save to user_job_preferences table
      const { error: preferencesError } = await supabase
        .from('user_job_preferences')
        .upsert({
          user_id: user.id,
          preferred_locations: selectedLocations,
          preferred_job_types: selectedJobTypes,
          preferred_industries: selectedIndustries,
          experience_level: experienceLevel,
          min_salary: minSalary,
          max_salary: maxSalary,
          remote_work_preference: remotePreference,
          max_commute_distance: 50,
          willing_to_relocate: false,
          salary_negotiable: true,
          location_weight: 0.25,
          salary_weight: 0.30,
          role_weight: 0.25,
          company_weight: 0.20,
          auto_learn_from_swipes: true,
          updated_at: new Date().toISOString()
        });

      if (preferencesError) throw preferencesError;

      // Mark onboarding as complete
      const { error: profileError } = await supabase
        .from('profiles')
        .update({ onboarding_completed: true })
        .eq('id', user.id);

      if (profileError) throw profileError;

      Alert.alert(
        'Setup Complete!',
        'Your job preferences have been saved. You\'ll now see personalized job recommendations.',
        [
          {
            text: 'Start Browsing Jobs',
            onPress: () => router.replace('/(tabs)/index')
          }
        ]
      );
    } catch (error) {
      console.error('Error saving preferences:', error);
      setError('Failed to save preferences. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: themeColors.text }]}>
            Let's personalize your job search
          </Text>
          <Text style={[styles.subtitle, { color: themeColors.textSecondary }]}>
            Tell us what you're looking for and we'll find the perfect jobs for you
          </Text>
        </View>

        {/* Location Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
            📍 Where do you want to work?
          </Text>
          
          <View style={styles.locationInputContainer}>
            <View style={styles.inputContainer}>
              <TextInput
                style={[styles.textInput, { 
                  backgroundColor: themeColors.card,
                  color: themeColors.text,
                  borderColor: themeColors.border
                }]}
                placeholder="Enter city, state (e.g., San Francisco, CA)"
                placeholderTextColor={themeColors.textSecondary}
                value={locationInput}
                onChangeText={handleLocationInputChange}
                onSubmitEditing={() => selectLocation(locationInput)}
              />
            </View>
            
            {showLocationSuggestions && (
              <View style={[styles.suggestionsContainer, { backgroundColor: themeColors.card, borderColor: themeColors.border }]}>
                {locationSuggestions.map((suggestion, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[styles.suggestionItem, { borderBottomColor: themeColors.border }]}
                    onPress={() => selectLocation(suggestion)}
                  >
                    <FontAwesome name="map-marker" size={14} color={themeColors.textSecondary} style={styles.suggestionIcon} />
                    <Text style={[styles.suggestionText, { color: themeColors.text }]}>{suggestion}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>

          <View style={styles.tagContainer}>
            {selectedLocations.map((location, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.tag, { backgroundColor: themeColors.tint }]}
                onPress={() => removeLocation(index)}
              >
                <Text style={styles.tagText}>{location} ×</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Job Types Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
            💼 What type of work? ({selectedJobTypes.length} selected)
          </Text>
          <View style={styles.optionsGrid}>
            {JOB_TYPES.map((jobType) => (
              <TouchableOpacity
                key={jobType}
                style={[
                  styles.optionChip,
                  {
                    backgroundColor: selectedJobTypes.includes(jobType)
                      ? themeColors.tint 
                      : themeColors.card,
                    borderColor: themeColors.border
                  }
                ]}
                onPress={() => toggleJobType(jobType)}
              >
                <Text style={[
                  styles.optionChipText,
                  {
                    color: selectedJobTypes.includes(jobType)
                      ? '#FFFFFF' 
                      : themeColors.text
                  }
                ]}>
                  {jobType}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Experience Level Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
            🎯 Experience Level
          </Text>
          {EXPERIENCE_LEVELS.map((level) => (
            <TouchableOpacity
              key={level.key}
              style={[
                styles.experienceOption,
                {
                  backgroundColor: experienceLevel === level.key
                    ? themeColors.tint
                    : themeColors.card,
                  borderColor: themeColors.border
                }
              ]}
              onPress={() => setExperienceLevel(level.key)}
            >
              <Text style={[
                styles.experienceOptionText,
                {
                  color: experienceLevel === level.key
                    ? '#FFFFFF'
                    : themeColors.text
                }
              ]}>
                {level.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Salary Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
            💰 Salary Range
          </Text>
          
          <View style={styles.salaryContainer}>
            <Text style={[styles.salaryLabel, { color: themeColors.text }]}>
              ${minSalary.toLocaleString()} - ${maxSalary.toLocaleString()}
            </Text>
            
            <View style={styles.sliderContainer}>
              <Text style={[styles.sliderLabel, { color: themeColors.textSecondary }]}>Min</Text>
              <Slider
                style={styles.slider}
                minimumValue={20000}
                maximumValue={200000}
                step={5000}
                value={minSalary}
                onValueChange={setMinSalary}
                minimumTrackTintColor={themeColors.tint}
                maximumTrackTintColor={themeColors.border}
              />
              <Text style={[styles.sliderLabel, { color: themeColors.textSecondary }]}>Max</Text>
              <Slider
                style={styles.slider}
                minimumValue={minSalary + 10000}
                maximumValue={500000}
                step={5000}
                value={maxSalary}
                onValueChange={setMaxSalary}
                minimumTrackTintColor={themeColors.tint}
                maximumTrackTintColor={themeColors.border}
              />
            </View>
          </View>
        </View>

        {error ? (
          <Text style={[styles.errorText, { color: themeColors.error }]}>
            {error}
          </Text>
        ) : null}
      </ScrollView>

      <View style={[styles.buttonContainer, { backgroundColor: themeColors.background }]}>
        <TouchableOpacity
          style={[
            styles.saveButton,
            { backgroundColor: themeColors.tint },
            loading && styles.disabledButton
          ]}
          onPress={handleSavePreferences}
          disabled={loading}
        >
          <Text style={styles.saveButtonText}>
            {loading ? 'Saving...' : 'Save Preferences & Continue'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    paddingVertical: 30,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 15,
  },
  locationInputContainer: {
    position: 'relative',
    marginBottom: 15,
  },
  inputContainer: {
    marginBottom: 10,
  },
  textInput: {
    height: 50,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  suggestionsContainer: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    borderWidth: 1,
    borderTopWidth: 0,
    borderRadius: 12,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    maxHeight: 200,
    zIndex: 1000,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
  },
  suggestionIcon: {
    marginRight: 8,
  },
  suggestionText: {
    fontSize: 16,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  tagText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  optionChip: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 25,
    borderWidth: 1,
  },
  optionChipText: {
    fontSize: 14,
    fontWeight: '500',
  },
  experienceOption: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 8,
  },
  experienceOptionText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  salaryContainer: {
    alignItems: 'center',
  },
  salaryLabel: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 20,
  },
  sliderContainer: {
    width: '100%',
    alignItems: 'center',
  },
  sliderLabel: {
    fontSize: 14,
    marginBottom: 5,
  },
  slider: {
    width: '100%',
    height: 40,
    marginBottom: 15,
  },
  buttonContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  saveButton: {
    height: 56,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.6,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
    marginVertical: 10,
    paddingHorizontal: 20,
  },
});
