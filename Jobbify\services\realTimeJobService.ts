import { supabase } from '@/lib/supabase';

export interface UserJobPreferences {
  user_id: string;
  preferred_locations: string[];
  preferred_job_types: string[];
  preferred_industries: string[];
  experience_level: string;
  min_salary: number;
  max_salary: number;
  remote_work_preference: string;
}

export interface JobSearchParams {
  keywords: string;
  location: string;
  jobType?: string;
  experienceLevel?: string;
  minSalary?: number;
  maxSalary?: number;
}

export interface CachedJobSearch {
  id: string;
  search_params: JobSearchParams;
  results: any[];
  created_at: string;
  expires_at: string;
  user_count: number;
}

// Cache duration in hours
const CACHE_DURATION_HOURS = 4;

/**
 * Generate a cache key for job search parameters
 */
function generateCacheKey(params: JobSearchParams): string {
  const { keywords, location, jobType, experienceLevel } = params;
  return `${keywords}_${location}_${jobType || 'any'}_${experienceLevel || 'any'}`
    .toLowerCase()
    .replace(/\s+/g, '_')
    .replace(/[^a-z0-9_]/g, '');
}

/**
 * Check if cached results exist for given search parameters
 */
export async function getCachedJobResults(params: JobSearchParams): Promise<any[] | null> {
  try {
    const cacheKey = generateCacheKey(params);
    const now = new Date().toISOString();
    
    const { data, error } = await supabase
      .from('cached_job_searches')
      .select('results, expires_at')
      .eq('cache_key', cacheKey)
      .gt('expires_at', now)
      .single();
    
    if (error || !data) {
      return null;
    }
    
    console.log(`✅ Cache hit for: ${cacheKey}`);
    return data.results;
  } catch (error) {
    console.error('Error checking cache:', error);
    return null;
  }
}

/**
 * Cache job search results
 */
export async function cacheJobResults(params: JobSearchParams, results: any[]): Promise<void> {
  try {
    const cacheKey = generateCacheKey(params);
    const now = new Date();
    const expiresAt = new Date(now.getTime() + CACHE_DURATION_HOURS * 60 * 60 * 1000);
    
    const { error } = await supabase
      .from('cached_job_searches')
      .upsert({
        cache_key: cacheKey,
        search_params: params,
        results: results,
        created_at: now.toISOString(),
        expires_at: expiresAt.toISOString(),
        user_count: 1
      });
    
    if (error) {
      console.error('Error caching results:', error);
    } else {
      console.log(`💾 Cached ${results.length} jobs for: ${cacheKey}`);
    }
  } catch (error) {
    console.error('Error caching job results:', error);
  }
}

/**
 * Fetch jobs from multiple APIs based on user preferences
 */
export async function fetchJobsForUser(userId: string): Promise<any[]> {
  try {
    // Get user preferences
    const { data: preferences, error: prefError } = await supabase
      .from('user_job_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (prefError || !preferences) {
      console.error('No preferences found for user:', userId);
      return [];
    }
    
    console.log('🔍 Fetching jobs for user preferences:', preferences);
    
    // Generate search combinations based on user preferences
    const searchCombinations = generateSearchCombinations(preferences);
    
    let allJobs: any[] = [];
    
    // Process each search combination
    for (const searchParams of searchCombinations) {
      // Check cache first
      const cachedResults = await getCachedJobResults(searchParams);
      
      if (cachedResults) {
        allJobs.push(...cachedResults);
        continue;
      }
      
      // If not cached, fetch from APIs
      const freshResults = await fetchJobsFromAPIs(searchParams);
      
      if (freshResults.length > 0) {
        // Cache the results
        await cacheJobResults(searchParams, freshResults);
        allJobs.push(...freshResults);
      }
      
      // Add delay to respect API rate limits
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Remove duplicates and sort by relevance
    const uniqueJobs = removeDuplicateJobs(allJobs);
    const rankedJobs = rankJobsByPreferences(uniqueJobs, preferences);
    
    console.log(`✅ Fetched ${rankedJobs.length} unique jobs for user`);
    return rankedJobs;
    
  } catch (error) {
    console.error('Error fetching jobs for user:', error);
    return [];
  }
}

/**
 * Generate search combinations based on user preferences
 */
function generateSearchCombinations(preferences: UserJobPreferences): JobSearchParams[] {
  const combinations: JobSearchParams[] = [];
  
  // Create combinations of locations and job types
  for (const location of preferences.preferred_locations) {
    for (const jobType of preferences.preferred_job_types) {
      // Use industries as keywords if available
      if (preferences.preferred_industries.length > 0) {
        for (const industry of preferences.preferred_industries.slice(0, 3)) { // Limit to top 3 industries
          combinations.push({
            keywords: `${industry} ${jobType}`,
            location: location,
            jobType: jobType,
            experienceLevel: preferences.experience_level,
            minSalary: preferences.min_salary,
            maxSalary: preferences.max_salary
          });
        }
      } else {
        // Generic search if no industries specified
        combinations.push({
          keywords: jobType,
          location: location,
          jobType: jobType,
          experienceLevel: preferences.experience_level,
          minSalary: preferences.min_salary,
          maxSalary: preferences.max_salary
        });
      }
    }
  }
  
  // Limit total combinations to prevent API overuse
  return combinations.slice(0, 10);
}

/**
 * Fetch jobs from multiple APIs
 */
async function fetchJobsFromAPIs(params: JobSearchParams): Promise<any[]> {
  const jobs: any[] = [];
  
  try {
    console.log(`🔄 Fetching fresh jobs for: ${params.keywords} in ${params.location}`);
    
    // Call your FastAPI backend
    const response = await fetch('http://localhost:8000/jobs/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        keywords: params.keywords,
        location: params.location,
        job_type: params.jobType,
        experience_level: params.experienceLevel,
        min_salary: params.minSalary,
        max_salary: params.maxSalary
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      jobs.push(...data.jobs || []);
    }
  } catch (error) {
    console.error('Error fetching from APIs:', error);
  }
  
  return jobs;
}

/**
 * Remove duplicate jobs based on title and company
 */
function removeDuplicateJobs(jobs: any[]): any[] {
  const seen = new Set();
  return jobs.filter(job => {
    const key = `${job.title}_${job.company}`.toLowerCase();
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
}

/**
 * Rank jobs by user preferences
 */
function rankJobsByPreferences(jobs: any[], preferences: UserJobPreferences): any[] {
  return jobs.map(job => {
    let score = 0;
    
    // Location match
    if (preferences.preferred_locations.some(loc => 
      job.location?.toLowerCase().includes(loc.toLowerCase())
    )) {
      score += 30;
    }
    
    // Job type match
    if (preferences.preferred_job_types.some(type => 
      job.title?.toLowerCase().includes(type.toLowerCase()) ||
      job.employment_type?.toLowerCase().includes(type.toLowerCase())
    )) {
      score += 25;
    }
    
    // Industry match
    if (preferences.preferred_industries.some(industry => 
      job.description?.toLowerCase().includes(industry.toLowerCase()) ||
      job.company?.toLowerCase().includes(industry.toLowerCase())
    )) {
      score += 20;
    }
    
    // Salary match
    if (job.salary_min && job.salary_max) {
      const jobMinSalary = parseInt(job.salary_min);
      const jobMaxSalary = parseInt(job.salary_max);
      
      if (jobMinSalary >= preferences.min_salary && jobMaxSalary <= preferences.max_salary) {
        score += 25;
      } else if (jobMinSalary <= preferences.max_salary && jobMaxSalary >= preferences.min_salary) {
        score += 15; // Partial overlap
      }
    }
    
    return { ...job, relevance_score: score };
  }).sort((a, b) => b.relevance_score - a.relevance_score);
}

/**
 * Get user preferences
 */
export async function getUserPreferences(userId: string): Promise<UserJobPreferences | null> {
  try {
    const { data, error } = await supabase
      .from('user_job_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (error) {
      console.error('Error fetching user preferences:', error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error getting user preferences:', error);
    return null;
  }
}

/**
 * Update user preferences
 */
export async function updateUserPreferences(preferences: Partial<UserJobPreferences>): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('user_job_preferences')
      .upsert(preferences);
    
    if (error) {
      console.error('Error updating preferences:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error updating user preferences:', error);
    return false;
  }
}
