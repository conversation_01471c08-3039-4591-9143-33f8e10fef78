#!/usr/bin/env python3
"""
Test script for Redis cache and API tracking system
"""

import asyncio
import json
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_redis_cache():
    """Test Redis cache functionality"""
    print("🧪 Testing Redis Cache System")
    print("=" * 50)
    
    try:
        from cache import cache, get_cached_jobs, cache_jobs, track_api_usage
        
        # Test connection
        await cache.connect()
        print(f"✅ Redis connection: {cache.connected}")
        
        # Test cache operations
        test_data = {
            "jobs": [
                {"title": "Test Job 1", "company": "Test Corp", "location": "San Francisco, CA"},
                {"title": "Test Job 2", "company": "Another Corp", "location": "Remote"}
            ],
            "cached_at": datetime.now().isoformat()
        }
        
        # Test cache set
        success = await cache_jobs(
            location="San Francisco, CA",
            job_types=["Full-time"],
            experience="mid",
            industries=["Technology"],
            jobs_data=test_data
        )
        print(f"✅ Cache set successful: {success}")
        
        # Test cache get
        cached_data = await get_cached_jobs(
            location="San Francisco, CA",
            job_types=["Full-time"],
            experience="mid",
            industries=["Technology"]
        )
        print(f"✅ Cache get successful: {cached_data is not None}")
        
        if cached_data:
            print(f"   Cached jobs count: {len(cached_data.get('data', {}).get('jobs', []))}")
        
        # Test API usage tracking
        usage_count = await track_api_usage("test_api")
        print(f"✅ API usage tracking: {usage_count}")
        
        # Test cache stats
        stats = await cache.get_cache_stats()
        print(f"✅ Cache stats: {stats}")
        
        await cache.disconnect()
        
    except Exception as e:
        print(f"❌ Redis cache test failed: {e}")
        import traceback
        traceback.print_exc()

async def test_api_usage_tracking():
    """Test API usage tracking system"""
    print("\n🧪 Testing API Usage Tracking")
    print("=" * 50)
    
    try:
        from api_usage import usage_tracker, call_with_tracking, APILimitReached
        
        # Test getting usage record
        record = await usage_tracker.get_usage_record("test_api")
        print(f"✅ Usage record retrieved: {record['api_name']}")
        print(f"   Calls made: {record['calls_made']}")
        print(f"   Quota remaining: {record['quota_remaining']}")
        
        # Test quota check
        has_quota = await usage_tracker.check_quota("test_api")
        print(f"✅ Quota check: {has_quota}")
        
        # Test usage stats
        stats = await usage_tracker.get_all_usage_stats()
        print(f"✅ Usage stats retrieved for {len(stats.get('apis', {}))} APIs")
        
        # Test API call with tracking
        async def mock_api_call():
            await asyncio.sleep(0.1)  # Simulate API call
            return {"test": "data"}
        
        try:
            result = await call_with_tracking("test_api", mock_api_call())
            print(f"✅ Tracked API call successful: {result}")
        except APILimitReached as e:
            print(f"⚠️ API limit reached: {e}")
        
    except Exception as e:
        print(f"❌ API usage tracking test failed: {e}")
        import traceback
        traceback.print_exc()

async def test_personalized_search_integration():
    """Test the integrated personalized search system"""
    print("\n🧪 Testing Personalized Search Integration")
    print("=" * 50)
    
    try:
        # Create test user preferences
        test_user_id = "test-user-cache-integration"
        test_preferences = {
            "user_id": test_user_id,
            "preferred_locations": ["San Francisco, CA"],
            "preferred_job_types": ["Full-time"],
            "preferred_industries": ["Technology"],
            "experience_level": "mid",
            "min_salary": 80000,
            "max_salary": 150000,
            "plan": "pro"  # Bypass rate limits for testing
        }
        
        # Insert test preferences
        from supabase_client import supabase
        supabase.table("user_job_preferences").upsert(test_preferences).execute()
        print("✅ Test user preferences created")
        
        # Test personalized search endpoint
        from personalized_search import PersonalizedSearchRequest, personalized_job_search
        
        request = PersonalizedSearchRequest(
            user_id=test_user_id,
            force_refresh=False,  # Test cache first
            limit=10
        )
        
        # First call (should miss cache and fetch from APIs)
        print("🔄 First search call (cache miss expected)...")
        result1 = await personalized_job_search(request)
        print(f"✅ First search: {len(result1.jobs)} jobs, cache used: {result1.cache_used}")
        
        # Second call (should hit cache)
        print("🔄 Second search call (cache hit expected)...")
        result2 = await personalized_job_search(request)
        print(f"✅ Second search: {len(result2.jobs)} jobs, cache used: {result2.cache_used}")
        
        # Clean up test data
        supabase.table("user_job_preferences").delete().eq("user_id", test_user_id).execute()
        print("✅ Test user preferences cleaned up")
        
    except Exception as e:
        print(f"❌ Personalized search integration test failed: {e}")
        import traceback
        traceback.print_exc()

async def test_rate_limiting():
    """Test user rate limiting"""
    print("\n🧪 Testing Rate Limiting")
    print("=" * 50)
    
    try:
        from cache import get_user_search_count, increment_user_searches
        
        test_user_id = "rate-limit-test-user"
        
        # Test initial count
        initial_count = await get_user_search_count(test_user_id)
        print(f"✅ Initial search count: {initial_count}")
        
        # Test increment
        new_count = await increment_user_searches(test_user_id)
        print(f"✅ After increment: {new_count}")
        
        # Test multiple increments
        for i in range(3):
            count = await increment_user_searches(test_user_id)
            print(f"   Search {i+2}: {count}")
        
    except Exception as e:
        print(f"❌ Rate limiting test failed: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Run all tests"""
    print("🚀 Starting Cache & API Tracking System Tests")
    print("=" * 60)
    
    # Test Redis cache
    await test_redis_cache()
    
    # Test API usage tracking
    await test_api_usage_tracking()
    
    # Test rate limiting
    await test_rate_limiting()
    
    # Test integrated system
    await test_personalized_search_integration()
    
    print("\n" + "=" * 60)
    print("✅ All tests completed!")
    print("\nNext steps:")
    print("1. Start Redis server: redis-server")
    print("2. Start FastAPI server: python -m uvicorn main:app --reload")
    print("3. Test endpoints:")
    print("   - GET /health")
    print("   - GET /metrics") 
    print("   - GET /cache/stats")
    print("   - POST /api/jobs/personalized-search")
    print("4. Run cache warming: python warm_cache.py")

if __name__ == "__main__":
    asyncio.run(main())
