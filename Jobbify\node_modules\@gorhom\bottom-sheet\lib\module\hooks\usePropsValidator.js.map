{"version": 3, "names": ["invariant", "useMemo", "INITIAL_SNAP_POINT", "usePropsValidator", "index", "snapPoints", "enableDynamicSizing", "topInset", "bottomInset", "_snapPoints", "get", "map", "snapPoint", "_snapPoint", "Number", "parseInt", "replace", "length"], "sourceRoot": "../../../src", "sources": ["hooks/usePropsValidator.ts"], "mappings": ";;AAAA,OAAOA,SAAS,MAAM,WAAW;AACjC,SAASC,OAAO,QAAQ,OAAO;AAE/B,SAASC,kBAAkB,QAAQ,qCAAqC;;AAExE;AACA;AACA;AACA;;AAEA,OAAO,MAAMC,iBAAiB,GAAGA,CAAC;EAChCC,KAAK;EACLC,UAAU;EACVC,mBAAmB;EACnBC,QAAQ;EACRC;AAIF,CAAC,KAAK;EACJP,OAAO,CAAC,MAAM;IACZ;IACA,MAAMQ,WAAW,GAAGJ,UAAU,GAC1B,KAAK,IAAIA,UAAU,GACjBA,UAAU,CAACK,GAAG,CAAC,CAAC,GAChBL,UAAU,GACZ,EAAE;IACNL,SAAS,CACPS,WAAW,IAAIH,mBAAmB,EAClC,wEACF,CAAC;IAEDG,WAAW,CAACE,GAAG,CAACC,SAAS,IAAI;MAC3B,MAAMC,UAAU,GACd,OAAOD,SAAS,KAAK,QAAQ,GACzBA,SAAS,GACTE,MAAM,CAACC,QAAQ,CAACH,SAAS,CAACI,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;MAErDhB,SAAS,CACPa,UAAU,GAAG,CAAC,IAAIA,UAAU,KAAKX,kBAAkB,EACnD,eAAeU,SAAS,qGAC1B,CAAC;IACH,CAAC,CAAC;IAEFZ,SAAS,CACP,OAAO,IAAIS,WAAW,IAAIA,WAAW,CAACQ,MAAM,GAAG,CAAC,IAAIX,mBAAmB,EACvE,mFACF,CAAC;IACD;;IAEA;IACAN,SAAS,CACP,OAAOI,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,WAAW,EACzD,uEACF,CAAC;IAEDJ,SAAS,CACPM,mBAAmB,KAChB,OAAOF,KAAK,KAAK,QAAQ,GACtBA,KAAK,IAAI,CAAC,CAAC,IAAIA,KAAK,IAAIK,WAAW,CAACQ,MAAM,GAAG,CAAC,GAC9C,IAAI,CAAC,EACX,oGACER,WAAW,CAACQ,MAAM,GAAG,CAAC,EAE1B,CAAC;IACD;;IAEA;IACAjB,SAAS,CACP,OAAOO,QAAQ,KAAK,QAAQ,IAAI,OAAOA,QAAQ,KAAK,WAAW,EAC/D,0EACF,CAAC;IACDP,SAAS,CACP,OAAOQ,WAAW,KAAK,QAAQ,IAAI,OAAOA,WAAW,KAAK,WAAW,EACrE,6EACF,CAAC;IACD;;IAEA;EACF,CAAC,EAAE,CAACJ,KAAK,EAAEC,UAAU,EAAEE,QAAQ,EAAEC,WAAW,EAAEF,mBAAmB,CAAC,CAAC;AACrE,CAAC", "ignoreList": []}