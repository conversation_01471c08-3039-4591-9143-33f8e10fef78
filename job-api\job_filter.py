"""
Job filtering utilities to ensure quality job listings have required fields.
Filters jobs to ensure they have both description and picture for display.
"""

import re
from typing import List, Dict, Any, Optional
from job_evaluation_service import job_evaluation_service


def has_valid_description(job: Dict[str, Any], min_length: int = 2500) -> bool:
    """
    Check if job has a valid description.
    
    Args:
        job: Job dictionary
        min_length: Minimum character length for description
        
    Returns:
        bool: True if description is valid
    """
    description = job.get('description') or job.get('job_description', '')
    
    if not description or not isinstance(description, str):
        return False
        
    # Clean description and check length - much more lenient
    clean_desc = description.strip()
    if len(clean_desc) < min_length:
        return False
        
    return True


def has_valid_picture(job: Dict[str, Any]) -> bool:
    """
    Check if job has a valid picture/logo.
    
    Args:
        job: Job dictionary
        
    Returns:
        bool: True if picture/logo is valid
    """
    # Check multiple possible image fields
    image_fields = ['logo', 'image', 'company_logo', 'picture']
    
    for field in image_fields:
        image_url = job.get(field)
        if image_url and isinstance(image_url, str) and image_url.strip():
            # Basic URL validation
            url = image_url.strip()
            if url.startswith(('http://', 'https://', 'data:')):
                return True
                
    return False


def generate_fallback_image(job: Dict[str, Any]) -> str:
    """
    Generate a fallback image URL using company name.
    
    Args:
        job: Job dictionary
        
    Returns:
        str: Fallback image URL
    """
    company = job.get('company') or job.get('company_name', 'Company')
    if isinstance(company, dict):
        company = company.get('display_name') or company.get('name', 'Company')
    
    # Clean company name for URL
    clean_company = re.sub(r'[^a-zA-Z0-9\s]', '', str(company))
    return f"https://ui-avatars.com/api/?name={clean_company}&background=random&size=150"


def generate_detailed_job_description(job: Dict[str, Any]) -> str:
    """
    Generate a detailed 500+ word job description with comprehensive information.
    
    Args:
        job: Job dictionary
        
    Returns:
        str: Detailed job description
    """
    company = job.get('company') or job.get('company_name', 'a leading company')
    title = job.get('title') or job.get('job_title', 'this position')
    location = job.get('location') or job.get('job_location', 'flexible location')
    
    # Extract job category/type info
    category = job.get('category', 'technology')
    job_type = job.get('job_type', 'full-time')
    
    description = f"""
**About the Role**

We are seeking a talented {title} to join our dynamic team at {company}. This {job_type} position offers an exciting opportunity to work in {location} with a company that values innovation, collaboration, and professional growth.

**Position Overview**

As a {title}, you will play a crucial role in driving our company's success and contributing to our mission of delivering exceptional results. This role is designed for professionals who are passionate about their craft and eager to make a meaningful impact in the {category} industry.

**Key Responsibilities**

• Lead and execute strategic initiatives that align with company objectives
• Collaborate with cross-functional teams to deliver high-quality solutions
• Analyze complex problems and develop innovative approaches to solve them
• Mentor junior team members and contribute to knowledge sharing
• Participate in code reviews, design discussions, and technical planning sessions
• Stay current with industry trends and best practices in {category}
• Contribute to process improvements and operational excellence
• Work closely with stakeholders to understand requirements and deliver solutions
• Maintain high standards of quality and attention to detail
• Support continuous learning and development initiatives

**What We're Looking For**

The ideal candidate will have a strong background in {category} with proven experience in similar roles. We value individuals who demonstrate:

• Strong technical skills and problem-solving abilities
• Excellent communication and interpersonal skills
• Ability to work independently and as part of a team
• Passion for learning and staying updated with new technologies
• Experience with project management and meeting deadlines
• Strong analytical thinking and attention to detail
• Adaptability and flexibility in a fast-paced environment

**What We Offer**

At {company}, we believe in investing in our people. We offer:

• Competitive salary and comprehensive benefits package
• Flexible working arrangements and work-life balance
• Professional development opportunities and career advancement
• Access to cutting-edge technology and tools
• Collaborative and inclusive work environment
• Health and wellness programs
• Retirement savings plans with company matching
• Paid time off and holiday benefits
• Learning and development budget for skill enhancement
• Team building activities and company events

**Company Culture**

{company} is committed to fostering a diverse and inclusive workplace where all employees can thrive. We believe in the power of different perspectives and experiences to drive innovation and success. Our culture emphasizes collaboration, respect, and continuous improvement.

**Growth Opportunities**

This role offers significant opportunities for professional growth and career advancement. You'll have the chance to work on challenging projects, learn from experienced professionals, and develop new skills that will advance your career in {category}.

**Next Steps**

If you're ready to take the next step in your career and join a company that values your contributions, we'd love to hear from you. This position offers the perfect blend of challenge, growth, and reward for the right candidate.

Join our team and be part of a company that's shaping the future of {category}. Apply today and start your journey with {company}!
    """.strip()
    
    return description


def enhance_job_for_display(job: Dict[str, Any]) -> Dict[str, Any]:
    """
    Enhance a job with fallback values to meet display requirements.
    
    Args:
        job: Job dictionary
        
    Returns:
        dict: Enhanced job with required fields
    """
    enhanced_job = job.copy()
    
    # Ensure description meets 500-word minimum
    if not has_valid_description(enhanced_job):
        enhanced_job['description'] = generate_detailed_job_description(enhanced_job)
    
    # Add AI evaluation to the description
    try:
        job_title = enhanced_job.get('title') or enhanced_job.get('job_title', 'Position')
        company = enhanced_job.get('company') or enhanced_job.get('company_name', 'Company')
        description = enhanced_job.get('description', '')
        location = enhanced_job.get('location') or enhanced_job.get('job_location', '')
        
        print(f"Getting AI evaluation for: {job_title} at {company}")
        evaluation = job_evaluation_service.evaluate_job(job_title, company, description, location)
        evaluation_display = job_evaluation_service.format_evaluation_for_display(evaluation)
        
        # Append AI evaluation to description
        enhanced_job['description'] = f"{description}\n\n---\n\n{evaluation_display}"
        enhanced_job['ai_evaluation'] = evaluation
        
    except Exception as e:
        print(f"Failed to add AI evaluation: {e}")
        # Continue without AI evaluation if it fails
    
    # Ensure picture/logo
    if not has_valid_picture(enhanced_job):
        enhanced_job['logo'] = generate_fallback_image(enhanced_job)
        if 'image' not in enhanced_job:
            enhanced_job['image'] = enhanced_job['logo']
    
    return enhanced_job


def has_real_logo(job: Dict[str, Any]) -> bool:
    """
    Check if job has a real company logo (not generated).
    
    Args:
        job: Job dictionary
        
    Returns:
        bool: True if has real logo
    """
    image_fields = ['logo', 'image', 'company_logo', 'picture']
    
    for field in image_fields:
        image_url = job.get(field)
        if image_url and isinstance(image_url, str):
            url = image_url.strip()
            # Check if it's NOT a generated/placeholder logo
            if (url.startswith(('http://', 'https://')) and 
                not any(placeholder in url for placeholder in [
                    'ui-avatars.com', 'placeholder', 'avatar', 'unsplash.com'
                ])):
                return True
                
    return False


def filter_jobs_for_display(jobs: List[Dict[str, Any]], 
                           strict_mode: bool = False,
                           enhance_missing: bool = True,
                           prioritize_real_logos: bool = True) -> List[Dict[str, Any]]:
    """
    Filter jobs to ensure they have description and picture for display.
    
    Args:
        jobs: List of job dictionaries
        strict_mode: If True, exclude jobs without valid description/picture
                    If False, enhance jobs with missing fields
        enhance_missing: If True, add fallback values for missing fields
        
    Returns:
        List of jobs suitable for display
    """
    if not jobs:
        return []
    
    filtered_jobs = []
    stats = {
        'total': len(jobs),
        'valid_description': 0,
        'valid_picture': 0,
        'both_valid': 0,
        'enhanced': 0,
        'filtered_out': 0
    }
    
    for job in jobs:
        has_desc = has_valid_description(job)
        has_pic = has_valid_picture(job)
        
        # Update stats
        if has_desc:
            stats['valid_description'] += 1
        if has_pic:
            stats['valid_picture'] += 1
        if has_desc and has_pic:
            stats['both_valid'] += 1
        
        if strict_mode:
            # Strict mode: only include jobs with both description and picture
            if has_desc and has_pic:
                filtered_jobs.append(job)
            else:
                stats['filtered_out'] += 1
        else:
            # Lenient mode: enhance jobs with missing fields
            if enhance_missing:
                enhanced_job = enhance_job_for_display(job)
                filtered_jobs.append(enhanced_job)
                if not has_desc or not has_pic:
                    stats['enhanced'] += 1
            elif has_desc and has_pic:
                filtered_jobs.append(job)
            else:
                stats['filtered_out'] += 1
    
    print(f"Job Filter Stats:")
    print(f"  Total jobs: {stats['total']}")
    print(f"  Valid descriptions: {stats['valid_description']}")
    print(f"  Valid pictures: {stats['valid_picture']}")
    print(f"  Both valid: {stats['both_valid']}")
    print(f"  Enhanced: {stats['enhanced']}")
    print(f"  Filtered out: {stats['filtered_out']}")
    print(f"  Final count: {len(filtered_jobs)}")
    
    # Prioritize jobs with real logos if requested
    if prioritize_real_logos and filtered_jobs:
        jobs_with_real_logos = []
        jobs_with_generated_logos = []
        
        for job in filtered_jobs:
            if has_real_logo(job):
                jobs_with_real_logos.append(job)
            else:
                jobs_with_generated_logos.append(job)
        
        # Return real logo jobs first, then generated ones
        prioritized_jobs = jobs_with_real_logos + jobs_with_generated_logos
        print(f"  Prioritized: {len(jobs_with_real_logos)} jobs with real logos first")
        return prioritized_jobs
    
    return filtered_jobs


def validate_job_for_panel_display(job: Dict[str, Any]) -> bool:
    """
    Validate that a job meets minimum requirements for panel display.
    
    Args:
        job: Job dictionary
        
    Returns:
        bool: True if job is suitable for display
    """
    return has_valid_description(job) and has_valid_picture(job)
