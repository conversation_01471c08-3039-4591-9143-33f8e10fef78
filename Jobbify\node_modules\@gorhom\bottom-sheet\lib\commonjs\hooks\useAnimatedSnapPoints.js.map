{"version": 3, "names": ["_reactNativeReanimated", "require", "_constants", "_utilities", "useAnimatedSnapPoints", "snapPoints", "containerHeight", "contentHeight", "handleHeight", "footerHeight", "enableDynamicSizing", "maxDynamicContentSize", "dynamicSnapPointIndex", "useSharedValue", "normalizedSnapPoints", "useDerivedValue", "isContainerLayoutReady", "value", "INITIAL_CONTAINER_HEIGHT", "INITIAL_SNAP_POINT", "_snapPoints", "_normalizedSnapPoints", "map", "snapPoint", "normalizeSnapPoint", "INITIAL_HANDLE_HEIGHT", "dynamicSnapPoint", "Math", "min", "undefined", "includes", "push", "sort", "a", "b", "indexOf", "hasDynamicSnapPoint", "length", "find", "exports"], "sourceRoot": "../../../src", "sources": ["hooks/useAnimatedSnapPoints.ts"], "mappings": ";;;;;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAMA,IAAAC,UAAA,GAAAD,OAAA;AAKA,IAAAE,UAAA,GAAAF,OAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMG,qBAAqB,GAAGA,CACnCC,UAA0C,EAC1CC,eAAoC,EACpCC,aAAkC,EAClCC,YAAiC,EACjCC,YAAiC,EACjCC,mBAA4D,EAC5DC,qBAAgE,KACO;EACvE,MAAMC,qBAAqB,GAAG,IAAAC,qCAAc,EAAS,CAAC,CAAC,CAAC;EACxD,MAAMC,oBAAoB,GAAG,IAAAC,sCAAe,EAAC,MAAM;IACjD;IACA,MAAMC,sBAAsB,GAC1BV,eAAe,CAACW,KAAK,KAAKC,mCAAwB;IACpD,IAAI,CAACF,sBAAsB,EAAE;MAC3B,OAAO,CAACG,6BAAkB,CAAC;IAC7B;;IAEA;IACA,MAAMC,WAAW,GAAGf,UAAU,GAC1B,OAAO,IAAIA,UAAU,GACnBA,UAAU,CAACY,KAAK,GAChBZ,UAAU,GACZ,EAAE;;IAEN;IACA;IACA,IAAIgB,qBAAqB,GAAGD,WAAW,CAACE,GAAG,CAACC,SAAS,IACnD,IAAAC,6BAAkB,EAACD,SAAS,EAAEjB,eAAe,CAACW,KAAK,CACrD,CAAa;;IAEb;IACA,IAAI,CAACP,mBAAmB,EAAE;MACxB,OAAOW,qBAAqB;IAC9B;;IAEA;IACA,IAAIb,YAAY,CAACS,KAAK,KAAKQ,gCAAqB,EAAE;MAChD,OAAO,CAACN,6BAAkB,CAAC;IAC7B;;IAEA;IACA,IAAIZ,aAAa,CAACU,KAAK,KAAKC,mCAAwB,EAAE;MACpD,OAAO,CAACC,6BAAkB,CAAC;IAC7B;;IAEA;IACA,MAAMO,gBAAgB,GACpBpB,eAAe,CAACW,KAAK,GACrBU,IAAI,CAACC,GAAG,CACNrB,aAAa,CAACU,KAAK,GAAGT,YAAY,CAACS,KAAK,EACxCN,qBAAqB,KAAKkB,SAAS,GAC/BlB,qBAAqB,GACrBL,eAAe,CAACW,KACtB,CAAC;;IAEH;IACA;IACA,IAAI,CAACI,qBAAqB,CAACS,QAAQ,CAACJ,gBAAgB,CAAC,EAAE;MACrDL,qBAAqB,CAACU,IAAI,CAACL,gBAAgB,CAAC;IAC9C;;IAEA;IACAL,qBAAqB,GAAGA,qBAAqB,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;;IAEnE;IACArB,qBAAqB,CAACK,KAAK,GACzBI,qBAAqB,CAACc,OAAO,CAACT,gBAAgB,CAAC;IAEjD,OAAOL,qBAAqB;EAC9B,CAAC,EAAE,CACDhB,UAAU,EACVC,eAAe,EACfE,YAAY,EACZD,aAAa,EACbE,YAAY,EACZC,mBAAmB,EACnBC,qBAAqB,EACrBC,qBAAqB,CACtB,CAAC;EAEF,MAAMwB,mBAAmB,GAAG,IAAArB,sCAAe,EAAC,MAAM;IAChD;AACJ;AACA;IACI,IAAIL,mBAAmB,EAAE;MACvB,OAAO,IAAI;IACb;;IAEA;IACA,MAAMU,WAAW,GAAGf,UAAU,GAC1B,OAAO,IAAIA,UAAU,GACnBA,UAAU,CAACY,KAAK,GAChBZ,UAAU,GACZ,EAAE;;IAEN;AACJ;AACA;IACI,IACEe,WAAW,CAACiB,MAAM,IAClBjB,WAAW,CAACkB,IAAI,CAACf,SAAS,IAAI,OAAOA,SAAS,KAAK,QAAQ,CAAC,EAC5D;MACA,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd,CAAC,CAAC;EAEF,OAAO,CAACT,oBAAoB,EAAEF,qBAAqB,EAAEwB,mBAAmB,CAAC;AAC3E,CAAC;AAACG,OAAA,CAAAnC,qBAAA,GAAAA,qBAAA", "ignoreList": []}