{"version": 3, "names": ["_react", "require", "_gesture", "useBottomSheetGestureHandlers", "context", "useContext", "BottomSheetGestureHandlersContext", "exports"], "sourceRoot": "../../../src", "sources": ["hooks/useBottomSheetGestureHandlers.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAEO,MAAME,6BAA6B,GAAGA,CAAA,KAAM;EACjD,MAAMC,OAAO,GAAG,IAAAC,iBAAU,EAACC,0CAAiC,CAAC;EAE7D,IAAIF,OAAO,KAAK,IAAI,EAAE;IACpB,MAAM,wEAAwE;EAChF;EAEA,OAAOA,OAAO;AAChB,CAAC;AAACG,OAAA,CAAAJ,6BAAA,GAAAA,6BAAA", "ignoreList": []}