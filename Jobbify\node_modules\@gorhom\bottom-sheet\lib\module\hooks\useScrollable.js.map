{"version": 3, "names": ["useCallback", "useRef", "useSharedValue", "SCROLLABLE_STATE", "SCROLLABLE_TYPE", "findNodeHandle", "useScrollable", "scrollableRef", "previousScrollableRef", "animatedScrollableType", "UNDETERMINED", "animatedScrollableContentOffsetY", "animatedScrollableOverrideState", "isScrollableRefreshable", "setScrollableRef", "ref", "currentRefId", "current", "id", "removeScrollableRef"], "sourceRoot": "../../../src", "sources": ["hooks/useScrollable.ts"], "mappings": ";;AAAA,SAAyBA,WAAW,EAAEC,MAAM,QAAQ,OAAO;AAE3D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,EAAEC,eAAe,QAAQ,cAAc;AAEhE,SAASC,cAAc,QAAQ,cAAc;AAE7C,OAAO,MAAMC,aAAa,GAAGA,CAAA,KAAM;EACjC;EACA,MAAMC,aAAa,GAAGN,MAAM,CAAgB,IAAI,CAAC;EACjD,MAAMO,qBAAqB,GAAGP,MAAM,CAAgB,IAAI,CAAC;;EAEzD;EACA,MAAMQ,sBAAsB,GAAGP,cAAc,CAC3CE,eAAe,CAACM,YAClB,CAAC;EACD,MAAMC,gCAAgC,GAAGT,cAAc,CAAS,CAAC,CAAC;EAClE,MAAMU,+BAA+B,GAAGV,cAAc,CACpDC,gBAAgB,CAACO,YACnB,CAAC;EACD,MAAMG,uBAAuB,GAAGX,cAAc,CAAU,KAAK,CAAC;;EAE9D;EACA,MAAMY,gBAAgB,GAAGd,WAAW,CAAEe,GAAkB,IAAK;IAC3D;IACA,MAAMC,YAAY,GAAGT,aAAa,CAACU,OAAO,EAAEC,EAAE,IAAI,IAAI;IAEtD,IAAIF,YAAY,KAAKD,GAAG,CAACG,EAAE,EAAE;MAC3B,IAAIX,aAAa,CAACU,OAAO,EAAE;QACzB;QACAT,qBAAqB,CAACS,OAAO,GAAGV,aAAa,CAACU,OAAO;MACvD;MACA;MACAV,aAAa,CAACU,OAAO,GAAGF,GAAG;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,mBAAmB,GAAGnB,WAAW,CAAEe,GAA0B,IAAK;IACtE;IACA,IAAIG,EAAqB;IACzB,IAAI;MACFA,EAAE,GAAGb,cAAc,CAACU,GAAG,CAACE,OAAO,CAAC;IAClC,CAAC,CAAC,MAAM;MACN;IACF;;IAEA;IACA,MAAMD,YAAY,GAAGT,aAAa,CAACU,OAAO,EAAEC,EAAE,IAAI,IAAI;;IAEtD;AACJ;AACA;AACA;AACA;IACI,IAAIA,EAAE,KAAKF,YAAY,EAAE;MACvB;MACAT,aAAa,CAACU,OAAO,GAAGT,qBAAqB,CAACS,OAAO;IACvD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLV,aAAa;IACbE,sBAAsB;IACtBE,gCAAgC;IAChCC,+BAA+B;IAC/BC,uBAAuB;IACvBC,gBAAgB;IAChBK;EACF,CAAC;AACH,CAAC", "ignoreList": []}