{"version": 3, "names": ["_react", "require", "_contexts", "useBottomSheetModal", "context", "useContext", "BottomSheetModalContext", "exports"], "sourceRoot": "../../../src", "sources": ["hooks/useBottomSheetModal.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AAEO,MAAME,mBAAmB,GAAGA,CAAA,KAAM;EACvC,MAAMC,OAAO,GAAG,IAAAC,iBAAU,EAACC,iCAAuB,CAAC;EAEnD,IAAIF,OAAO,KAAK,IAAI,EAAE;IACpB,MAAM,2CAA2C;EACnD;EAEA,OAAOA,OAAO;AAChB,CAAC;AAACG,OAAA,CAAAJ,mBAAA,GAAAA,mBAAA", "ignoreList": []}