{"version": 3, "names": ["State", "useWorkletCallback", "GESTURE_SOURCE", "useGestureHandler", "source", "state", "gestureSource", "onStart", "onChange", "onEnd", "onFinalize", "handleOnStart", "event", "value", "BEGAN", "handleOnChange", "handleOnEnd", "UNDETERMINED", "handleOnFinalize"], "sourceRoot": "../../../src", "sources": ["hooks/useGestureHandler.ts"], "mappings": ";;AAAA,SAKEA,KAAK,QACA,8BAA8B;AAErC,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,cAAc,QAAQ,cAAc;AAM7C,OAAO,MAAMC,iBAA0C,GAAGA,CACxDC,MAAsB,EACtBC,KAAyB,EACzBC,aAA0C,EAC1CC,OAAwC,EACxCC,QAAyC,EACzCC,KAAsC,EACtCC,UAA2C,KACxC;EACH,MAAMC,aAAa,GAAGV,kBAAkB,CACrCW,KAA6D,IAAK;IACjEP,KAAK,CAACQ,KAAK,GAAGb,KAAK,CAACc,KAAK;IACzBR,aAAa,CAACO,KAAK,GAAGT,MAAM;IAE5BG,OAAO,CAACH,MAAM,EAAEQ,KAAK,CAAC;IACtB;EACF,CAAC,EACD,CAACP,KAAK,EAAEC,aAAa,EAAEF,MAAM,EAAEG,OAAO,CACxC,CAAC;EAED,MAAMQ,cAAc,GAAGd,kBAAkB,CAErCW,KAEC,IACE;IACH,IAAIN,aAAa,CAACO,KAAK,KAAKT,MAAM,EAAE;MAClC;IACF;IAEAC,KAAK,CAACQ,KAAK,GAAGD,KAAK,CAACP,KAAK;IACzBG,QAAQ,CAACJ,MAAM,EAAEQ,KAAK,CAAC;EACzB,CAAC,EACD,CAACP,KAAK,EAAEC,aAAa,EAAEF,MAAM,EAAEI,QAAQ,CACzC,CAAC;EAED,MAAMQ,WAAW,GAAGf,kBAAkB,CACnCW,KAA6D,IAAK;IACjE,IAAIN,aAAa,CAACO,KAAK,KAAKT,MAAM,EAAE;MAClC;IACF;IAEAC,KAAK,CAACQ,KAAK,GAAGD,KAAK,CAACP,KAAK;IACzBC,aAAa,CAACO,KAAK,GAAGX,cAAc,CAACe,YAAY;IAEjDR,KAAK,CAACL,MAAM,EAAEQ,KAAK,CAAC;EACtB,CAAC,EACD,CAACP,KAAK,EAAEC,aAAa,EAAEF,MAAM,EAAEK,KAAK,CACtC,CAAC;EAED,MAAMS,gBAAgB,GAAGjB,kBAAkB,CACxCW,KAA6D,IAAK;IACjE,IAAIN,aAAa,CAACO,KAAK,KAAKT,MAAM,EAAE;MAClC;IACF;IAEAC,KAAK,CAACQ,KAAK,GAAGD,KAAK,CAACP,KAAK;IACzBC,aAAa,CAACO,KAAK,GAAGX,cAAc,CAACe,YAAY;IAEjDP,UAAU,CAACN,MAAM,EAAEQ,KAAK,CAAC;EAC3B,CAAC,EACD,CAACP,KAAK,EAAEC,aAAa,EAAEF,MAAM,EAAEM,UAAU,CAC3C,CAAC;EAED,OAAO;IACLC,aAAa;IACbI,cAAc;IACdC,WAAW;IACXE;EACF,CAAC;AACH,CAAC", "ignoreList": []}