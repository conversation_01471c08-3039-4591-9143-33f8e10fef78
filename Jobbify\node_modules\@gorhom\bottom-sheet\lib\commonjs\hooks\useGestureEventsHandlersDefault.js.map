{"version": 3, "names": ["_reactNative", "require", "_reactNativeReanimated", "_constants", "_clamp", "_snapPoint", "_useBottomSheetInternal", "INITIAL_CONTEXT", "initialPosition", "initialKeyboardState", "KEYBOARD_STATE", "UNDETERMINED", "isScrollablePositionLocked", "dismissKeyboard", "Keyboard", "dismiss", "resetContext", "context", "Object", "keys", "map", "key", "undefined", "useGestureEventsHandlersDefault", "animatedPosition", "animatedSnapPoints", "animatedKeyboardState", "animatedKeyboardHeight", "animatedContainerHeight", "animatedScrollableType", "animatedHighestSnapPoint", "animatedClosedPosition", "animatedScrollableContentOffsetY", "enableOverDrag", "enablePanDownToClose", "overDragResistanceFactor", "isInTemporaryPosition", "isScrollableRefreshable", "enableBlurKeyboardOnGesture", "animateToPosition", "stopAnimation", "useBottomSheetInternal", "useSharedValue", "handleOnStart", "useWorkletCallback", "__", "_", "value", "SHOWN", "HIDDEN", "runOnJS", "handleOnChange", "source", "translationY", "highestSnapPoint", "lowestSnapPoint", "GESTURE_SOURCE", "CONTENT", "negativeScrollableContentOffset", "draggedPosition", "accumulatedDraggedPosition", "clampedPosition", "clamp", "HANDLE", "SCROLLABLE_TYPE", "VIEW", "resistedPosition", "Math", "sqrt", "handleOnEnd", "absoluteY", "velocityY", "isSheetAtHighestSnapPoint", "ANIMATION_SOURCE", "GESTURE", "isScrollable", "Platform", "OS", "WINDOW_HEIGHT", "snapPoints", "slice", "unshift", "destinationPoint", "snapPoint", "wasGestureHandledByScrollView", "handleOnFinalize", "exports"], "sourceRoot": "../../../src", "sources": ["hooks/useGestureEventsHandlersDefault.tsx"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAD,OAAA;AAKA,IAAAE,UAAA,GAAAF,OAAA;AAWA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAJ,OAAA;AACA,IAAAK,uBAAA,GAAAL,OAAA;AAQA,MAAMM,eAAwC,GAAG;EAC/CC,eAAe,EAAE,CAAC;EAClBC,oBAAoB,EAAEC,yBAAc,CAACC,YAAY;EACjDC,0BAA0B,EAAE;AAC9B,CAAC;AAED,MAAMC,eAAe,GAAGC,qBAAQ,CAACC,OAAO;;AAExC;AACA,MAAMC,YAAY,GAAIC,OAAY,IAAK;EACrC,SAAS;;EACTC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,GAAG,CAACC,GAAG,IAAI;IAC9BJ,OAAO,CAACI,GAAG,CAAC,GAAGC,SAAS;EAC1B,CAAC,CAAC;AACJ,CAAC;AAEM,MAAMC,+BAA8D,GACzEA,CAAA,KAAM;EACJ;EACA,MAAM;IACJC,gBAAgB;IAChBC,kBAAkB;IAClBC,qBAAqB;IACrBC,sBAAsB;IACtBC,uBAAuB;IACvBC,sBAAsB;IACtBC,wBAAwB;IACxBC,sBAAsB;IACtBC,gCAAgC;IAChCC,cAAc;IACdC,oBAAoB;IACpBC,wBAAwB;IACxBC,qBAAqB;IACrBC,uBAAuB;IACvBC,2BAA2B;IAC3BC,iBAAiB;IACjBC;EACF,CAAC,GAAG,IAAAC,8CAAsB,EAAC,CAAC;EAE5B,MAAMxB,OAAO,GAAG,IAAAyB,qCAAc,EAA0B;IACtD,GAAGnC;EACL,CAAC,CAAC;EACF;;EAEA;EACA,MAAMoC,aAA8C,GAAG,IAAAC,yCAAkB,EACvE,SAASD,aAAaA,CAACE,EAAE,EAAEC,CAAC,EAAE;IAC5B;IACAN,aAAa,CAAC,CAAC;IAEf,IAAI/B,oBAAoB,GAAGiB,qBAAqB,CAACqB,KAAK;IACtD;IACA,IACET,2BAA2B,IAC3B7B,oBAAoB,KAAKC,yBAAc,CAACsC,KAAK,EAC7C;MACAvC,oBAAoB,GAAGC,yBAAc,CAACuC,MAAM;MAC5C,IAAAC,8BAAO,EAACrC,eAAe,CAAC,CAAC,CAAC;IAC5B;;IAEA;IACAI,OAAO,CAAC8B,KAAK,GAAG;MACd,GAAG9B,OAAO,CAAC8B,KAAK;MAChBvC,eAAe,EAAEgB,gBAAgB,CAACuB,KAAK;MACvCtC,oBAAoB,EAAEiB,qBAAqB,CAACqB;IAC9C,CAAC;;IAED;AACR;AACA;AACA;IACQ,IAAIf,gCAAgC,CAACe,KAAK,GAAG,CAAC,EAAE;MAC9C9B,OAAO,CAAC8B,KAAK,GAAG;QACd,GAAG9B,OAAO,CAAC8B,KAAK;QAChBnC,0BAA0B,EAAE;MAC9B,CAAC;IACH;EACF,CAAC,EACD,CACE4B,aAAa,EACbF,2BAA2B,EAC3Bd,gBAAgB,EAChBE,qBAAqB,EACrBM,gCAAgC,CAEpC,CAAC;EACD,MAAMmB,cAA+C,GAAG,IAAAP,yCAAkB,EACxE,SAASO,cAAcA,CAACC,MAAM,EAAE;IAAEC;EAAa,CAAC,EAAE;IAChD,IAAIC,gBAAgB,GAAGxB,wBAAwB,CAACiB,KAAK;;IAErD;AACR;AACA;AACA;IACQ,IACEX,qBAAqB,CAACW,KAAK,IAC3B9B,OAAO,CAAC8B,KAAK,CAACtC,oBAAoB,KAAKC,yBAAc,CAACsC,KAAK,EAC3D;MACAM,gBAAgB,GAAGrC,OAAO,CAAC8B,KAAK,CAACvC,eAAe;IAClD;;IAEA;AACR;AACA;AACA;IACQ,IACE4B,qBAAqB,CAACW,KAAK,IAC3B9B,OAAO,CAAC8B,KAAK,CAACvC,eAAe,GAAG8C,gBAAgB,EAChD;MACAA,gBAAgB,GAAGrC,OAAO,CAAC8B,KAAK,CAACvC,eAAe;IAClD;IAEA,MAAM+C,eAAe,GAAGrB,oBAAoB,GACxCN,uBAAuB,CAACmB,KAAK,GAC7BtB,kBAAkB,CAACsB,KAAK,CAAC,CAAC,CAAC;;IAE/B;AACR;AACA;AACA;IACQ,IACEK,MAAM,KAAKI,yBAAc,CAACC,OAAO,IACjCpB,uBAAuB,CAACU,KAAK,IAC7BvB,gBAAgB,CAACuB,KAAK,KAAKO,gBAAgB,EAC3C;MACA;IACF;;IAEA;AACR;AACA;AACA;AACA;AACA;IACQ,MAAMI,+BAA+B,GAClCzC,OAAO,CAAC8B,KAAK,CAACvC,eAAe,KAAK8C,gBAAgB,IACjDF,MAAM,KAAKI,yBAAc,CAACC,OAAO,IACnC,CAACxC,OAAO,CAAC8B,KAAK,CAACnC,0BAA0B,GACrCoB,gCAAgC,CAACe,KAAK,GAAG,CAAC,CAAC,GAC3C,CAAC;;IAEP;AACR;AACA;IACQ,MAAMY,eAAe,GAAG1C,OAAO,CAAC8B,KAAK,CAACvC,eAAe,GAAG6C,YAAY;;IAEpE;AACR;AACA;AACA;AACA;IACQ,MAAMO,0BAA0B,GAC9BD,eAAe,GAAGD,+BAA+B;;IAEnD;AACR;AACA;AACA;IACQ,MAAMG,eAAe,GAAG,IAAAC,YAAK,EAC3BF,0BAA0B,EAC1BN,gBAAgB,EAChBC,eACF,CAAC;;IAED;AACR;AACA;AACA;IACQ,IACEtC,OAAO,CAAC8B,KAAK,CAACnC,0BAA0B,IACxCwC,MAAM,KAAKI,yBAAc,CAACC,OAAO,IACjCjC,gBAAgB,CAACuB,KAAK,KAAKO,gBAAgB,EAC3C;MACArC,OAAO,CAAC8B,KAAK,GAAG;QACd,GAAG9B,OAAO,CAAC8B,KAAK;QAChBnC,0BAA0B,EAAE;MAC9B,CAAC;IACH;;IAEA;AACR;AACA;IACQ,IAAIqB,cAAc,EAAE;MAClB,IACE,CAACmB,MAAM,KAAKI,yBAAc,CAACO,MAAM,IAC/BlC,sBAAsB,CAACkB,KAAK,KAAKiB,0BAAe,CAACC,IAAI,KACvDN,eAAe,GAAGL,gBAAgB,EAClC;QACA,MAAMY,gBAAgB,GACpBZ,gBAAgB,GAChBa,IAAI,CAACC,IAAI,CAAC,CAAC,IAAId,gBAAgB,GAAGK,eAAe,CAAC,CAAC,GACjDxB,wBAAwB;QAC5BX,gBAAgB,CAACuB,KAAK,GAAGmB,gBAAgB;QACzC;MACF;MAEA,IACEd,MAAM,KAAKI,yBAAc,CAACO,MAAM,IAChCJ,eAAe,GAAGJ,eAAe,EACjC;QACA,MAAMW,gBAAgB,GACpBX,eAAe,GACfY,IAAI,CAACC,IAAI,CAAC,CAAC,IAAIT,eAAe,GAAGJ,eAAe,CAAC,CAAC,GAChDpB,wBAAwB;QAC5BX,gBAAgB,CAACuB,KAAK,GAAGmB,gBAAgB;QACzC;MACF;MAEA,IACEd,MAAM,KAAKI,yBAAc,CAACC,OAAO,IACjCE,eAAe,GAAGD,+BAA+B,GAAGH,eAAe,EACnE;QACA,MAAMW,gBAAgB,GACpBX,eAAe,GACfY,IAAI,CAACC,IAAI,CACP,CAAC,IACET,eAAe,GACdD,+BAA+B,GAC/BH,eAAe,CACrB,CAAC,GACCpB,wBAAwB;QAC5BX,gBAAgB,CAACuB,KAAK,GAAGmB,gBAAgB;QACzC;MACF;IACF;IAEA1C,gBAAgB,CAACuB,KAAK,GAAGc,eAAe;EAC1C,CAAC,EACD,CACE5B,cAAc,EACdC,oBAAoB,EACpBC,wBAAwB,EACxBC,qBAAqB,EACrBC,uBAAuB,EACvBP,wBAAwB,EACxBF,uBAAuB,EACvBH,kBAAkB,EAClBD,gBAAgB,EAChBK,sBAAsB,EACtBG,gCAAgC,CAEpC,CAAC;EACD,MAAMqC,WAA4C,GAAG,IAAAzB,yCAAkB,EACrE,SAASyB,WAAWA,CAACjB,MAAM,EAAE;IAAEC,YAAY;IAAEiB,SAAS;IAAEC;EAAU,CAAC,EAAE;IACnE,MAAMjB,gBAAgB,GAAGxB,wBAAwB,CAACiB,KAAK;IACvD,MAAMyB,yBAAyB,GAC7BhD,gBAAgB,CAACuB,KAAK,KAAKO,gBAAgB;;IAE7C;AACR;AACA;AACA;IACQ,IACEF,MAAM,KAAKI,yBAAc,CAACC,OAAO,IACjCpB,uBAAuB,CAACU,KAAK,IAC7ByB,yBAAyB,EACzB;MACA;IACF;;IAEA;AACR;AACA;AACA;IACQ,IACEpC,qBAAqB,CAACW,KAAK,IAC3B9B,OAAO,CAAC8B,KAAK,CAACvC,eAAe,IAAIgB,gBAAgB,CAACuB,KAAK,EACvD;MACA,IAAI9B,OAAO,CAAC8B,KAAK,CAACvC,eAAe,GAAGgB,gBAAgB,CAACuB,KAAK,EAAE;QAC1DR,iBAAiB,CACftB,OAAO,CAAC8B,KAAK,CAACvC,eAAe,EAC7BiE,2BAAgB,CAACC,OAAO,EACxBH,SAAS,GAAG,CACd,CAAC;MACH;MACA;IACF;;IAEA;AACR;AACA;AACA;IACQ,MAAMI,YAAY,GAChB9C,sBAAsB,CAACkB,KAAK,KAAKiB,0BAAe,CAACrD,YAAY,IAC7DkB,sBAAsB,CAACkB,KAAK,KAAKiB,0BAAe,CAACC,IAAI;;IAEvD;AACR;AACA;AACA;IACQ,IACEhD,OAAO,CAAC8B,KAAK,CAACtC,oBAAoB,KAAKC,yBAAc,CAACsC,KAAK,IAC3DxB,gBAAgB,CAACuB,KAAK,GAAG9B,OAAO,CAAC8B,KAAK,CAACvC,eAAe,EACtD;MACA;AACV;AACA;AACA;AACA;AACA;AACA;MACU,IACE,EACEoE,qBAAQ,CAACC,EAAE,KAAK,KAAK,IACrBF,YAAY,IACZL,SAAS,GAAGQ,wBAAa,GAAGnD,sBAAsB,CAACoB,KAAK,CACzD,EACD;QACA,IAAAG,8BAAO,EAACrC,eAAe,CAAC,CAAC,CAAC;MAC5B;IACF;;IAEA;AACR;AACA;IACQ,IAAIuB,qBAAqB,CAACW,KAAK,EAAE;MAC/BX,qBAAqB,CAACW,KAAK,GAAG,KAAK;IACrC;;IAEA;AACR;AACA;AACA;IACQ,MAAMgC,UAAU,GAAGtD,kBAAkB,CAACsB,KAAK,CAACiC,KAAK,CAAC,CAAC;IACnD,IAAI9C,oBAAoB,EAAE;MACxB6C,UAAU,CAACE,OAAO,CAAClD,sBAAsB,CAACgB,KAAK,CAAC;IAClD;;IAEA;AACR;AACA;IACQ,MAAMmC,gBAAgB,GAAG,IAAAC,oBAAS,EAChC9B,YAAY,GAAGpC,OAAO,CAAC8B,KAAK,CAACvC,eAAe,EAC5C+D,SAAS,EACTQ,UACF,CAAC;;IAED;AACR;AACA;AACA;IACQ,IAAIG,gBAAgB,KAAK1D,gBAAgB,CAACuB,KAAK,EAAE;MAC/C;IACF;IAEA,MAAMqC,6BAA6B,GACjChC,MAAM,KAAKI,yBAAc,CAACC,OAAO,IACjCzB,gCAAgC,CAACe,KAAK,GAAG,CAAC;IAC5C;AACR;AACA;IACQ,IAAIqC,6BAA6B,IAAIZ,yBAAyB,EAAE;MAC9D;IACF;IAEAjC,iBAAiB,CACf2C,gBAAgB,EAChBT,2BAAgB,CAACC,OAAO,EACxBH,SAAS,GAAG,CACd,CAAC;EACH,CAAC,EACD,CACErC,oBAAoB,EACpBE,qBAAqB,EACrBC,uBAAuB,EACvBN,sBAAsB,EACtBD,wBAAwB,EACxBH,sBAAsB,EACtBH,gBAAgB,EAChBK,sBAAsB,EACtBJ,kBAAkB,EAClBO,gCAAgC,EAChCO,iBAAiB,CAErB,CAAC;EAED,MAAM8C,gBAAiD,GACrD,IAAAzC,yCAAkB,EAChB,SAASyC,gBAAgBA,CAAA,EAAG;IAC1BrE,YAAY,CAACC,OAAO,CAAC;EACvB,CAAC,EACD,CAACA,OAAO,CACV,CAAC;EACH;;EAEA,OAAO;IACL0B,aAAa;IACbQ,cAAc;IACdkB,WAAW;IACXgB;EACF,CAAC;AACH,CAAC;AAACC,OAAA,CAAA/D,+BAAA,GAAAA,+BAAA", "ignoreList": []}