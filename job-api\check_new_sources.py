#!/usr/bin/env python3
import requests

def check_sources():
    """Check jobs from new sources in the database"""
    print("🔍 Checking jobs from new sources...")
    
    try:
        response = requests.get("http://localhost:8000/jobs/", params={"limit": 200})
        jobs = response.json()
        
        print(f"Total jobs available: {len(jobs)}")
        
        # Count jobs by source
        source_counts = {}
        jooble_jobs = []
        muse_jobs = []
        
        for job in jobs:
            description = job.get('description', '') or ''
            if 'SOURCE: jooble' in description:
                source_counts['jooble'] = source_counts.get('jooble', 0) + 1
                jooble_jobs.append(job)
            elif 'SOURCE: muse' in description:
                source_counts['muse'] = source_counts.get('muse', 0) + 1
                muse_jobs.append(job)
            elif 'SOURCE: remoteok' in description:
                source_counts['remoteok'] = source_counts.get('remoteok', 0) + 1
            elif 'SOURCE: arbeitnow' in description:
                source_counts['arbeitnow'] = source_counts.get('arbeitnow', 0) + 1
            elif 'SOURCE: adzuna' in description:
                source_counts['adzuna'] = source_counts.get('adzuna', 0) + 1
            else:
                source_counts['other'] = source_counts.get('other', 0) + 1
        
        print("\nJobs by source:")
        for source, count in source_counts.items():
            print(f"  {source}: {count} jobs")
        
        if jooble_jobs:
            print(f"\n📋 Sample Jooble jobs:")
            for i, job in enumerate(jooble_jobs[:3]):
                print(f"  {i+1}. {job.get('title', 'No title')} at {job.get('company', 'No company')}")
        
        if muse_jobs:
            print(f"\n🎭 Sample Muse jobs:")
            for i, job in enumerate(muse_jobs[:3]):
                print(f"  {i+1}. {job.get('title', 'No title')} at {job.get('company', 'No company')}")
                
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_sources()
