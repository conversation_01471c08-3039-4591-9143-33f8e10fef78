import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, ScrollView } from 'react-native';
import { MaterialIcons, FontAwesome, Ionicons } from '@expo/vector-icons';
import { Job } from '@/context/AppContext';
import { jobEvaluationService, JobEvaluation } from '@/services/jobEvaluationService';

interface EnhancedAIJobEvaluationProps {
  job: Job;
  themeColors: {
    background: string;
    card: string;
    text: string;
    textSecondary: string;
    tint: string;
  };
  style?: any;
}

export const EnhancedAIJobEvaluationComponent: React.FC<EnhancedAIJobEvaluationProps> = ({ 
  job, 
  themeColors, 
  style 
}) => {
  const [evaluation, setEvaluation] = useState<JobEvaluation | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load AI evaluation
  useEffect(() => {
    if (!evaluation && !isLoading) {
      loadEvaluation();
    }
  }, [job.id]);

  const loadEvaluation = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await jobEvaluationService.evaluateJob(
        job.title,
        job.company,
        job.description,
        job.location
      );
      setEvaluation(result);
    } catch (err) {
      console.error('Failed to load AI evaluation:', err);
      setError('Failed to load AI evaluation');
    } finally {
      setIsLoading(false);
    }
  };

  const retryEvaluation = () => {
    setError(null);
    loadEvaluation();
  };

  const getScoreColor = (score: number): string => {
    if (score >= 8) return '#4CAF50'; // Green - Excellent
    if (score >= 6) return '#2196F3'; // Blue - Good
    if (score >= 4) return '#FF9800'; // Orange - Fair
    return '#F44336'; // Red - Poor
  };

  const getScoreEmoji = (score: number): string => {
    if (score >= 8) return '🌟';
    if (score >= 6) return '⭐';
    if (score >= 4) return '✨';
    return '⚠️';
  };

  const getComplexityEmoji = (complexity: string): string => {
    const emojis = {
      'entry': '🌱',
      'mid': '🚀', 
      'senior': '👑',
      'executive': '💎'
    };
    return emojis[complexity as keyof typeof emojis] || '🚀';
  };

  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: themeColors.card }, style]}>
        <View style={styles.header}>
          <View style={styles.titleRow}>
            <MaterialIcons name="psychology" size={20} color={themeColors.tint} />
            <Text style={[styles.title, { color: themeColors.text }]}>AI Job Analysis</Text>
          </View>
          <ActivityIndicator size="small" color={themeColors.tint} />
        </View>
        <Text style={[styles.loadingText, { color: themeColors.textSecondary }]}>
          Analyzing job posting...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, { backgroundColor: themeColors.card }, style]}>
        <View style={styles.header}>
          <View style={styles.titleRow}>
            <MaterialIcons name="error-outline" size={20} color="#F44336" />
            <Text style={[styles.title, { color: themeColors.text }]}>AI Analysis</Text>
          </View>
          <TouchableOpacity onPress={retryEvaluation} style={styles.retryButton}>
            <MaterialIcons name="refresh" size={16} color={themeColors.tint} />
          </TouchableOpacity>
        </View>
        <Text style={[styles.errorText, { color: '#F44336' }]}>
          {error}
        </Text>
      </View>
    );
  }

  if (!evaluation) {
    return null;
  }

  const scoreColor = getScoreColor(evaluation.overallScore);
  const scoreEmoji = getScoreEmoji(evaluation.overallScore);
  const complexityEmoji = getComplexityEmoji(evaluation.roleComplexity);

  return (
    <View style={[styles.container, { backgroundColor: themeColors.card }, style]}>
      {/* Header with score */}
      <TouchableOpacity 
        style={styles.header}
        onPress={() => setIsExpanded(!isExpanded)}
        activeOpacity={0.7}
      >
        <View style={styles.titleRow}>
          <MaterialIcons name="psychology" size={20} color={themeColors.tint} />
          <Text style={[styles.title, { color: themeColors.text }]}>AI Job Analysis</Text>
          <Text style={styles.scoreEmoji}>{scoreEmoji}</Text>
        </View>
        <View style={styles.scoreContainer}>
          <Text style={[styles.score, { color: scoreColor }]}>
            {evaluation.overallScore}/10
          </Text>
          <MaterialIcons 
            name={isExpanded ? "expand-less" : "expand-more"} 
            size={20} 
            color={themeColors.textSecondary} 
          />
        </View>
      </TouchableOpacity>

      {/* Summary (always visible) */}
      <Text style={[styles.summary, { color: themeColors.textSecondary }]}>
        {evaluation.aiSummary}
      </Text>

      {/* Quick stats row */}
      <View style={styles.quickStatsRow}>
        <View style={styles.quickStat}>
          <Text style={styles.quickStatEmoji}>{complexityEmoji}</Text>
          <Text style={[styles.quickStatText, { color: themeColors.textSecondary }]}>
            {evaluation.roleComplexity.charAt(0).toUpperCase() + evaluation.roleComplexity.slice(1)}
          </Text>
        </View>
        <View style={styles.quickStat}>
          <Text style={styles.quickStatEmoji}>💰</Text>
          <Text style={[styles.quickStatText, { color: themeColors.textSecondary }]}>
            {evaluation.salaryEstimate.split(' ').slice(0, 2).join(' ')}
          </Text>
        </View>
        <View style={styles.quickStat}>
          <Text style={styles.quickStatEmoji}>🌐</Text>
          <Text style={[styles.quickStatText, { color: themeColors.textSecondary }]}>
            {evaluation.remoteWorkFriendly.includes('remote') || evaluation.remoteWorkFriendly.includes('Remote') ? 'Remote OK' : 'Office'}
          </Text>
        </View>
      </View>

      {/* Expandable details */}
      {isExpanded && (
        <ScrollView style={styles.expandedContent} showsVerticalScrollIndicator={false}>
          {/* Role Overview Card */}
          <View style={[styles.card, { backgroundColor: themeColors.background }]}>
            <View style={styles.cardHeader}>
              <MaterialIcons name="work" size={18} color={themeColors.tint} />
              <Text style={[styles.cardTitle, { color: themeColors.text }]}>Role Overview</Text>
            </View>
            <View style={styles.cardContent}>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: themeColors.textSecondary }]}>Level:</Text>
                <Text style={[styles.infoValue, { color: themeColors.text }]}>
                  {complexityEmoji} {evaluation.roleComplexity.charAt(0).toUpperCase() + evaluation.roleComplexity.slice(1)}
                </Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: themeColors.textSecondary }]}>Time:</Text>
                <Text style={[styles.infoValue, { color: themeColors.text }]}>{evaluation.timeCommitment}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: themeColors.textSecondary }]}>Travel:</Text>
                <Text style={[styles.infoValue, { color: themeColors.text }]}>{evaluation.travelRequirements}</Text>
              </View>
            </View>
          </View>

          {/* Compensation Card */}
          <View style={[styles.card, { backgroundColor: themeColors.background }]}>
            <View style={styles.cardHeader}>
              <MaterialIcons name="attach-money" size={18} color="#4CAF50" />
              <Text style={[styles.cardTitle, { color: themeColors.text }]}>Compensation</Text>
            </View>
            <View style={styles.cardContent}>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: themeColors.textSecondary }]}>Salary:</Text>
                <Text style={[styles.infoValue, { color: themeColors.text }]}>{evaluation.salaryEstimate}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: themeColors.textSecondary }]}>Industry:</Text>
                <Text style={[styles.infoValue, { color: themeColors.text }]}>{evaluation.industryOutlook}</Text>
              </View>
            </View>
          </View>

          {/* Company & Culture Card */}
          <View style={[styles.card, { backgroundColor: themeColors.background }]}>
            <View style={styles.cardHeader}>
              <MaterialIcons name="business" size={18} color="#2196F3" />
              <Text style={[styles.cardTitle, { color: themeColors.text }]}>Company & Culture</Text>
            </View>
            <View style={styles.cardContent}>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: themeColors.textSecondary }]}>Reputation:</Text>
                <Text style={[styles.infoValue, { color: themeColors.text }]}>{evaluation.companyReputation}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: themeColors.textSecondary }]}>Culture:</Text>
                <Text style={[styles.infoValue, { color: themeColors.text }]}>{evaluation.companyCulture}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: themeColors.textSecondary }]}>Security:</Text>
                <Text style={[styles.infoValue, { color: themeColors.text }]}>{evaluation.jobSecurity}</Text>
              </View>
            </View>
          </View>

          {/* Growth & Development Card */}
          <View style={[styles.card, { backgroundColor: themeColors.background }]}>
            <View style={styles.cardHeader}>
              <MaterialIcons name="trending-up" size={18} color="#FF9800" />
              <Text style={[styles.cardTitle, { color: themeColors.text }]}>Growth & Development</Text>
            </View>
            <View style={styles.cardContent}>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: themeColors.textSecondary }]}>Career Growth:</Text>
                <Text style={[styles.infoValue, { color: themeColors.text }]}>{evaluation.careerGrowth}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: themeColors.textSecondary }]}>Learning:</Text>
                <Text style={[styles.infoValue, { color: themeColors.text }]}>{evaluation.learningOpportunities}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: themeColors.textSecondary }]}>Work-Life:</Text>
                <Text style={[styles.infoValue, { color: themeColors.text }]}>{evaluation.workLifeBalance}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: themeColors.textSecondary }]}>Remote:</Text>
                <Text style={[styles.infoValue, { color: themeColors.text }]}>{evaluation.remoteWorkFriendly}</Text>
              </View>
            </View>
          </View>

          {/* Strengths Card */}
          {evaluation.strengths.length > 0 && (
            <View style={[styles.card, { backgroundColor: themeColors.background }]}>
              <View style={styles.cardHeader}>
                <MaterialIcons name="thumb-up" size={18} color="#4CAF50" />
                <Text style={[styles.cardTitle, { color: themeColors.text }]}>Key Strengths</Text>
              </View>
              <View style={styles.cardContent}>
                {evaluation.strengths.slice(0, 4).map((strength, index) => (
                  <View key={index} style={styles.bulletPoint}>
                    <Text style={[styles.bullet, { color: '#4CAF50' }]}>•</Text>
                    <Text style={[styles.bulletText, { color: themeColors.textSecondary }]}>
                      {strength}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Competitive Advantages Card */}
          {evaluation.competitiveAdvantages.length > 0 && (
            <View style={[styles.card, { backgroundColor: themeColors.background }]}>
              <View style={styles.cardHeader}>
                <MaterialIcons name="star" size={18} color="#FFD700" />
                <Text style={[styles.cardTitle, { color: themeColors.text }]}>Competitive Advantages</Text>
              </View>
              <View style={styles.cardContent}>
                {evaluation.competitiveAdvantages.slice(0, 3).map((advantage, index) => (
                  <View key={index} style={styles.bulletPoint}>
                    <Text style={[styles.bullet, { color: '#FFD700' }]}>★</Text>
                    <Text style={[styles.bulletText, { color: themeColors.textSecondary }]}>
                      {advantage}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Considerations Card */}
          {evaluation.concerns.length > 0 && (
            <View style={[styles.card, { backgroundColor: themeColors.background }]}>
              <View style={styles.cardHeader}>
                <MaterialIcons name="warning" size={18} color="#FF9800" />
                <Text style={[styles.cardTitle, { color: themeColors.text }]}>Considerations</Text>
              </View>
              <View style={styles.cardContent}>
                {evaluation.concerns.slice(0, 3).map((concern, index) => (
                  <View key={index} style={styles.bulletPoint}>
                    <Text style={[styles.bullet, { color: '#FF9800' }]}>⚠</Text>
                    <Text style={[styles.bulletText, { color: themeColors.textSecondary }]}>
                      {concern}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Red Flags Card */}
          {evaluation.redFlags.length > 0 && (
            <View style={[styles.card, { backgroundColor: themeColors.background }]}>
              <View style={styles.cardHeader}>
                <MaterialIcons name="flag" size={18} color="#F44336" />
                <Text style={[styles.cardTitle, { color: themeColors.text }]}>Red Flags</Text>
              </View>
              <View style={styles.cardContent}>
                {evaluation.redFlags.slice(0, 3).map((flag, index) => (
                  <View key={index} style={styles.bulletPoint}>
                    <Text style={[styles.bullet, { color: '#F44336' }]}>🚩</Text>
                    <Text style={[styles.bulletText, { color: themeColors.textSecondary }]}>
                      {flag}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Skills Required Card */}
          {evaluation.skillsRequired.length > 0 && (
            <View style={[styles.card, { backgroundColor: themeColors.background }]}>
              <View style={styles.cardHeader}>
                <MaterialIcons name="build" size={18} color={themeColors.tint} />
                <Text style={[styles.cardTitle, { color: themeColors.text }]}>Skills Required</Text>
              </View>
              <View style={styles.cardContent}>
                {evaluation.skillsRequired.slice(0, 5).map((skill, index) => (
                  <View key={index} style={styles.bulletPoint}>
                    <Text style={[styles.bullet, { color: themeColors.tint }]}>•</Text>
                    <Text style={[styles.bulletText, { color: themeColors.textSecondary }]}>
                      {skill}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  scoreEmoji: {
    fontSize: 16,
    marginLeft: 8,
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  score: {
    fontSize: 16,
    fontWeight: '700',
    marginRight: 4,
  },
  summary: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  loadingText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  errorText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  retryButton: {
    padding: 4,
  },
  quickStatsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 12,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  quickStat: {
    alignItems: 'center',
    flex: 1,
  },
  quickStatEmoji: {
    fontSize: 16,
    marginBottom: 4,
  },
  quickStatText: {
    fontSize: 12,
    textAlign: 'center',
  },
  expandedContent: {
    maxHeight: 400,
    marginTop: 8,
  },
  card: {
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  cardContent: {
    gap: 6,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 13,
    flex: 1,
  },
  infoValue: {
    fontSize: 13,
    fontWeight: '500',
    flex: 2,
    textAlign: 'right',
  },
  bulletPoint: {
    flexDirection: 'row',
    marginBottom: 6,
    alignItems: 'flex-start',
  },
  bullet: {
    fontSize: 14,
    marginRight: 8,
    fontWeight: 'bold',
    marginTop: 1,
    minWidth: 16,
  },
  bulletText: {
    fontSize: 13,
    flex: 1,
    lineHeight: 18,
  },
});
