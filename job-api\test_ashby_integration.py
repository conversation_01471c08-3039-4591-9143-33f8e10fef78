#!/usr/bin/env python3
import asyncio
import httpx
from ashby_service import ashby, map_ashby_job

async def test_ashby_api():
    """Test the Ashby API integration"""
    print("🔧 Testing Ashby API integration...")
    
    async with httpx.AsyncClient() as client:
        print("\n1. Testing Ashby API with default job board...")
        try:
            # Test with the default job board name (will likely fail but shows the API structure)
            ashby_jobs = await ashby(client)
            print(f"   ✅ Ashby returned {len(ashby_jobs)} jobs")
            if ashby_jobs:
                sample_job = ashby_jobs[0]
                print(f"   Sample job: {sample_job.get('title', 'No title')} at {sample_job.get('department', 'No department')}")
                mapped_job = map_ashby_job(sample_job)
                print(f"   Mapped job title: {mapped_job.get('title', 'No title')}")
                print(f"   Mapped job company: {mapped_job.get('company', 'No company')}")
                print(f"   Mapped job location: {mapped_job.get('location', 'No location')}")
        except Exception as e:
            print(f"   ⚠️ Ashby API Error (expected with default board name): {e}")
            print("   📝 To fix this, set ASHBY_JOB_BOARD_NAME environment variable with your actual job board name")
            print("   📝 Or update the ASHBY_JOB_BOARD_NAME variable in ashby_service.py")

        print("\n2. Testing Ashby API with some common job board names...")
        test_boards = ["ashby", "example", "demo"]
        for board in test_boards:
            try:
                print(f"   Testing with board name: {board}")
                ashby_jobs = await ashby(client, board)
                if ashby_jobs:
                    print(f"   ✅ Found {len(ashby_jobs)} jobs with board '{board}'")
                    break
                else:
                    print(f"   ⚠️ No jobs found for board '{board}'")
            except Exception as e:
                print(f"   ❌ Error with board '{board}': {e}")

if __name__ == "__main__":
    asyncio.run(test_ashby_api())
