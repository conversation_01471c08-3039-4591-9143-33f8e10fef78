#!/usr/bin/env python3
"""
Nightly Cache Cleanup Script
Removes expired cache entries and optimizes database performance
"""

import asyncio
import logging
from datetime import datetime, timedelta
from supabase_client import supabase
from cache import init_cache, cache

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CacheCleanup:
    def __init__(self):
        self.cleanup_stats = {
            "start_time": None,
            "end_time": None,
            "duration_seconds": 0,
            "expired_entries_removed": 0,
            "redis_keys_cleaned": 0,
            "database_rows_deleted": 0,
            "errors": []
        }
    
    async def cleanup_database_cache(self) -> int:
        """Clean up expired entries from cached_job_searches table"""
        try:
            logger.info("🧹 Starting database cache cleanup...")
            
            # Get count of expired entries first
            current_time = datetime.now().isoformat()
            
            count_response = supabase.table("cached_job_searches").select(
                "id", count="exact"
            ).lt("expires_at", current_time).execute()
            
            expired_count = count_response.count or 0
            logger.info(f"📊 Found {expired_count} expired cache entries")
            
            if expired_count == 0:
                return 0
            
            # Delete expired entries
            delete_response = supabase.table("cached_job_searches").delete().lt(
                "expires_at", current_time
            ).execute()
            
            deleted_count = len(delete_response.data) if delete_response.data else expired_count
            logger.info(f"✅ Deleted {deleted_count} expired cache entries from database")
            
            return deleted_count
            
        except Exception as e:
            error_msg = f"Error cleaning database cache: {e}"
            logger.error(error_msg)
            self.cleanup_stats["errors"].append(error_msg)
            return 0
    
    async def cleanup_redis_cache(self) -> int:
        """Clean up expired entries from Redis"""
        try:
            logger.info("🧹 Starting Redis cache cleanup...")
            
            if not cache.connected:
                await init_cache()
            
            if not cache.connected:
                logger.warning("Redis not connected, skipping Redis cleanup")
                return 0
            
            # Get all job cache keys
            job_keys = await cache.redis_client.keys("jobs:*")
            expired_count = 0
            
            for key in job_keys:
                try:
                    ttl = await cache.redis_client.ttl(key)
                    if ttl == -2:  # Key doesn't exist (expired)
                        expired_count += 1
                    elif ttl == -1:  # Key exists but no expiry (shouldn't happen)
                        # Set default expiry of 2 hours
                        await cache.redis_client.expire(key, 2 * 3600)
                        logger.debug(f"Set expiry for key without TTL: {key}")
                except Exception as e:
                    logger.warning(f"Error checking key {key}: {e}")
            
            # Clean up old metrics keys (older than 7 days)
            old_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            old_metric_keys = await cache.redis_client.keys(f"*:{old_date}")
            old_metric_keys.extend(await cache.redis_client.keys(f"*:202[0-3]-*"))  # Very old dates
            
            if old_metric_keys:
                deleted = await cache.redis_client.delete(*old_metric_keys)
                logger.info(f"🗑️ Deleted {deleted} old metric keys from Redis")
                expired_count += deleted
            
            logger.info(f"✅ Redis cleanup completed, {expired_count} keys processed")
            return expired_count
            
        except Exception as e:
            error_msg = f"Error cleaning Redis cache: {e}"
            logger.error(error_msg)
            self.cleanup_stats["errors"].append(error_msg)
            return 0
    
    async def cleanup_api_usage_logs(self) -> int:
        """Clean up old API usage tracking records (keep last 30 days)"""
        try:
            logger.info("🧹 Starting API usage logs cleanup...")
            
            # Keep last 30 days of API usage data
            cutoff_date = (datetime.now() - timedelta(days=30)).date().isoformat()
            
            # Get count first
            count_response = supabase.table("api_usage_tracking").select(
                "id", count="exact"
            ).lt("date", cutoff_date).execute()
            
            old_count = count_response.count or 0
            logger.info(f"📊 Found {old_count} old API usage records")
            
            if old_count == 0:
                return 0
            
            # Delete old records
            delete_response = supabase.table("api_usage_tracking").delete().lt(
                "date", cutoff_date
            ).execute()
            
            deleted_count = len(delete_response.data) if delete_response.data else old_count
            logger.info(f"✅ Deleted {deleted_count} old API usage records")
            
            return deleted_count
            
        except Exception as e:
            error_msg = f"Error cleaning API usage logs: {e}"
            logger.error(error_msg)
            self.cleanup_stats["errors"].append(error_msg)
            return 0
    
    async def cleanup_cache_warming_logs(self) -> int:
        """Clean up old cache warming logs (keep last 14 days)"""
        try:
            logger.info("🧹 Starting cache warming logs cleanup...")
            
            # Keep last 14 days of cache warming logs
            cutoff_date = (datetime.now() - timedelta(days=14)).date().isoformat()
            
            # Get count first
            count_response = supabase.table("cache_warming_logs").select(
                "id", count="exact"
            ).lt("run_date", cutoff_date).execute()
            
            old_count = count_response.count or 0
            logger.info(f"📊 Found {old_count} old cache warming logs")
            
            if old_count == 0:
                return 0
            
            # Delete old logs
            delete_response = supabase.table("cache_warming_logs").delete().lt(
                "run_date", cutoff_date
            ).execute()
            
            deleted_count = len(delete_response.data) if delete_response.data else old_count
            logger.info(f"✅ Deleted {deleted_count} old cache warming logs")
            
            return deleted_count
            
        except Exception as e:
            error_msg = f"Error cleaning cache warming logs: {e}"
            logger.error(error_msg)
            self.cleanup_stats["errors"].append(error_msg)
            return 0
    
    async def run_full_cleanup(self) -> dict:
        """Run complete cleanup process"""
        self.cleanup_stats["start_time"] = datetime.now()
        logger.info("🚀 Starting nightly cache cleanup process...")
        
        try:
            # Initialize cache connection
            await init_cache()
            
            # Run all cleanup tasks
            db_deleted = await self.cleanup_database_cache()
            redis_cleaned = await self.cleanup_redis_cache()
            api_logs_deleted = await self.cleanup_api_usage_logs()
            warming_logs_deleted = await self.cleanup_cache_warming_logs()
            
            # Update stats
            self.cleanup_stats["database_rows_deleted"] = db_deleted
            self.cleanup_stats["redis_keys_cleaned"] = redis_cleaned
            self.cleanup_stats["expired_entries_removed"] = db_deleted + redis_cleaned
            
            # Close cache connection
            await cache.disconnect()
            
        except Exception as e:
            error_msg = f"Error in cleanup process: {e}"
            logger.error(error_msg)
            self.cleanup_stats["errors"].append(error_msg)
        
        finally:
            self.cleanup_stats["end_time"] = datetime.now()
            self.cleanup_stats["duration_seconds"] = (
                self.cleanup_stats["end_time"] - self.cleanup_stats["start_time"]
            ).total_seconds()
        
        # Log cleanup summary
        logger.info(f"🏁 Cleanup completed in {self.cleanup_stats['duration_seconds']:.2f}s")
        logger.info(f"📊 Summary: {self.cleanup_stats['expired_entries_removed']} entries removed, "
                   f"{len(self.cleanup_stats['errors'])} errors")
        
        # Save cleanup log to database
        try:
            cleanup_log = {
                "run_date": datetime.now().date().isoformat(),
                "summary": self.cleanup_stats,
                "created_at": datetime.now().isoformat()
            }
            
            supabase.table("cache_cleanup_logs").upsert(cleanup_log).execute()
            logger.info("✅ Cleanup log saved to database")
            
        except Exception as e:
            logger.error(f"Failed to save cleanup log: {e}")
        
        return self.cleanup_stats

async def main():
    """Main entry point for cleanup script"""
    cleanup = CacheCleanup()
    
    try:
        stats = await cleanup.run_full_cleanup()
        
        # Print summary
        print(f"✅ Nightly cleanup completed successfully!")
        print(f"📊 Summary:")
        print(f"   - Duration: {stats['duration_seconds']:.2f} seconds")
        print(f"   - Database rows deleted: {stats['database_rows_deleted']}")
        print(f"   - Redis keys cleaned: {stats['redis_keys_cleaned']}")
        print(f"   - Total entries removed: {stats['expired_entries_removed']}")
        print(f"   - Errors: {len(stats['errors'])}")
        
        if stats['errors']:
            print("❌ Errors encountered:")
            for error in stats['errors']:
                print(f"   - {error}")
        
        return 0 if len(stats['errors']) == 0 else 1
        
    except Exception as e:
        logger.error(f"❌ Cleanup failed: {e}")
        print(f"❌ Cleanup failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
