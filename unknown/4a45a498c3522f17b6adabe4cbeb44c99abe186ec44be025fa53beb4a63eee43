export default {
  expo: {
    name: "Jobbify",
    slug: "jobbify",
    version: "1.0.0",
    scheme: "jobbify",
    sdkVersion: "53.0.0",
    extra: {
      SUPABASE_URL: "https://ubueawlkwlvgzxcslats.supabase.co",
      SUPABASE_ANON_KEY: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVidWVhd2xrd2x2Z3p4Y3NsYXRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0MzgzMTIsImV4cCI6MjA2MTAxNDMxMn0._YMssGKgq17C5XMkPnN5rq5Zhu_u4WsvNiveYIPd4lg",
      GOOGLE_IOS_CLIENT_ID: "363643098728-h7ardkqauedqsddnpri3ef026etkf4rs.apps.googleusercontent.com",
      GOOGLE_WEB_CLIENT_ID: "363643098728-153alopt4kga1c3fffp2cuuos3ethcch.apps.googleusercontent.com",
      // Ashby API Configuration - Demo setup using <PERSON><PERSON>'s own job board
      ASHBY_JOB_BOARD_NAME: "<PERSON><PERSON>", // Demo: Using <PERSON><PERSON>'s own job board (3 jobs available)
      ASHBY_INCLUDE_COMPENSATION: "true", // Include salary and compensation data
      eas: {
        projectId: "e7de3ae9-1f5f-4cb7-b0d2-2150d51197b2"
      }
    },
    plugins: [
      "expo-secure-store", 
      "expo-router",
      [
        "@react-native-google-signin/google-signin",
        {
          "webClientId": "363643098728-153alopt4kga1c3fffp2cuuos3ethcch.apps.googleusercontent.com",
          "iosUrlScheme": "com.googleusercontent.apps.363643098728-h7ardkqauedqsddnpri3ef026etkf4rs"
        }
      ]
    ],
    ios: {
      bundleIdentifier: "com.jobbify.mobile",
      infoPlist: {
        CFBundleURLTypes: [
          {
            CFBundleURLSchemes: [
              "com.googleusercontent.apps.363643098728-h7ardkqauedqsddnpri3ef026etkf4rs"
            ]
          }
        ]
      }
    },
    android: {
      package: "com.jobbify.mobile",
      adaptiveIcon: {
        foregroundImage: "./assets/images/adaptive-icon.png",
        backgroundColor: "#FFFFFF"
      }
    }
  }
}
