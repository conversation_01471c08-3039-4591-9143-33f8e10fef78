import * as FileSystem from 'expo-file-system';

export interface ResumeFile {
  name: string;
  uri: string;
  size: number;
  mimeType: string;
  lastModified?: number;
}

interface SkillAssessment {
  skill: string;
  level: number; // 1-10
  feedback: string;
}

interface ResumeSection {
  sectionName: string;
  score: number;
  summary: string;
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
  examples?: string[];
}

export interface KeySkill {
  skill: string;
  score: number;
  evidence?: string;
}

export interface JobMatch {
  id: string;
  title: string;
  company: string;
  matchScore: number;
  missingSkills: string[];
}

export interface ResumeAnalysis {
  overallSummary: string;
  overallScore: number;
  strengths: string[];
  areasForImprovement: string[];
  sections: ResumeSection[];
  keySkills: KeySkill[];
  jobMatches: JobMatch[];
  visualSuggestions: string[];
}

// Simulate resume content extraction based on file type
const extractResumeContent = async (file: ResumeFile): Promise<string> => {
  try {
    // In a real app, you would use different parsers based on file type
    // For PDF: pdf.js or react-native-pdf-lib
    // For DOCX: mammoth.js or similar
    // For this demo, we'll read the file as text if possible or simulate parsed content

    if (file.mimeType === 'text/plain') {
      // For text files, we can actually read the content
      const content = await FileSystem.readAsStringAsync(file.uri);
      return content;
    } else {
      // For other file types, in a real app you would send to a server
      // or use specialized libraries to extract text
      // Here we'll simulate a delay and return mock data
      await new Promise(resolve => setTimeout(resolve, 1500));
      return simulateResumeContent(file.name);
    }
  } catch (error) {
    console.error('Error extracting resume content:', error);
    return simulateResumeContent(file.name); // Fallback to mock data
  }
};

// Simulate resume content for demo purposes
const simulateResumeContent = (fileName: string): string => {
  return `
John Doe
<EMAIL> | (555) 123-4567 | linkedin.com/in/johndoe

SUMMARY
Results-driven software engineer with 5+ years of experience in developing scalable web applications using React, Node.js, and TypeScript. Passionate about creating elegant solutions to complex problems.

SKILLS
• Programming Languages: JavaScript, TypeScript, Python, Java
• Frontend: React, Angular, Vue.js, HTML5, CSS3, SASS
• Backend: Node.js, Express, Django, Spring Boot
• Databases: MongoDB, PostgreSQL, MySQL, SQL Server, Oracle, Redis
• DevOps: Docker, Kubernetes, AWS, CI/CD, Git
• Tools: Webpack, Jest, Cypress, Postman

PROFESSIONAL EXPERIENCE

Senior Software Engineer | TechCorp Inc. | Jan 2020 - Present
• Led the development of a customer-facing portal that increased user engagement by 35%
• Architected and implemented a microservices-based backend that improved system reliability by 40%
• Mentored junior developers and conducted code reviews to ensure code quality
• Implemented CI/CD pipelines that reduced deployment time by 50%

Software Engineer | InnovateSoft | Mar 2017 - Dec 2019
• Developed and maintained multiple React applications used by over 10,000 daily users
• Built RESTful APIs using Node.js and Express, interfacing with various database systems
• Collaborated with UX designers to implement responsive and accessible user interfaces
• Optimized database queries resulting in a 30% performance improvement

EDUCATION

Bachelor of Science in Computer Science | University of Technology | 2013 - 2017
• GPA: 3.8/4.0
• Relevant coursework: Data Structures, Algorithms, Database Systems, Web Development

CERTIFICATIONS
• AWS Certified Developer - Associate
• MongoDB Certified Developer
• React Native Certified Developer
`;
};

// Add a utility function to escape RegExp special characters
function escapeRegExp(str: string): string {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Analyze resume content
const analyzeResume = async (content: string): Promise<ResumeAnalysis> => {
  // Simulate processing delay
  await new Promise(resolve => setTimeout(resolve, 2000));

  // --- Textual & Content Checks ---
  const hasEmail = /[\w.-]+@[\w.-]+\.[\w.-]+/g.test(content);
  const hasPhone = /\(\d{3}\)\s*\d{3}-\d{4}|\d{3}-\d{3}-\d{4}/g.test(content);
  const hasLinkedIn = /linkedin\.com\/in\//g.test(content);
  const hasSummary = /SUMMARY|OBJECTIVE|PROFILE/gi.test(content);
  const hasSkills = /SKILLS|TECHNOLOGIES|TECHNICAL SKILLS/gi.test(content);
  const hasExperience = /EXPERIENCE|WORK HISTORY|EMPLOYMENT/gi.test(content);
  const hasEducation = /EDUCATION|ACADEMIC|UNIVERSITY|COLLEGE/gi.test(content);

  // --- Design & Layout Checks ---
  const lineCount = content.split('\n').length;
  const wordCount = content.split(/\s+/).length;
  const avgLineLength = wordCount / Math.max(lineCount, 1);
  const hasBulletPoints = /\u2022|\*/.test(content) || /\n\s*\-/.test(content);
  const hasSections = /\n[A-Z][A-Z ]{3,}\n/.test(content);
  const hasConsistentIndent = !(content.match(/\n\s{5,}\S/g) || []).length;
  const hasTables = /\|.*\|/.test(content) || /Table|tabular/i.test(content);
  const hasExcessiveLength = wordCount > 900;
  const hasShortLength = wordCount < 150;

  // --- Skill Extraction ---
  const skillsList = [
    'JavaScript', 'TypeScript', 'Python', 'Java', 'C++', 'C#', 'Go',
    'React', 'Angular', 'Vue.js', 'Next.js', 'Svelte', 'Redux',
    'Node.js', 'Express', 'Django', 'Flask', 'Spring', 'ASP.NET',
    'MongoDB', 'PostgreSQL', 'MySQL', 'SQL Server', 'Oracle', 'Redis',
    'AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'CI/CD',
    'REST API', 'GraphQL', 'Microservices', 'Serverless',
    'HTML', 'CSS', 'SASS', 'LESS', 'Bootstrap', 'Tailwind',
    'Git', 'Agile', 'Scrum', 'DevOps', 'TDD', 'BDD'
  ];
  const foundSkills: KeySkill[] = [];
  skillsList.forEach(skill => {
    const regex = new RegExp(escapeRegExp(skill), 'gi');
    if (regex.test(content)) {
      const score = 5 + Math.floor(Math.random() * 5); // Random score between 5-9
      foundSkills.push({
        skill,
        score,
        evidence: `Found ${skill} in resume content`
      });
    }
  });

  // --- Scoring ---
  let score = 0;
  // Contact info
  if (hasEmail) score += 5; else score -= 10;
  if (hasPhone) score += 5; else score -= 5;
  if (hasLinkedIn) score += 5;
  // Sections
  if (hasSummary) score += 10; else score -= 10;
  if (hasSkills) score += 15; else score -= 10;
  if (hasExperience) score += 20; else score -= 15;
  if (hasEducation) score += 10; else score -= 7;
  // Skills
  score += Math.min(foundSkills.length * 2, 10);
  // Design
  if (hasBulletPoints) score += 5; else score -= 5;
  if (hasSections) score += 5; else score -= 5;
  if (hasConsistentIndent) score += 3; else score -= 3;
  if (hasTables) score -= 2;
  // Layout
  if (hasExcessiveLength) score -= 15;
  if (hasShortLength) score -= 20;
  if (avgLineLength < 4) score -= 5;
  // Clamp score
  score = Math.max(0, Math.min(100, score));

  // --- Sections Feedback ---
  const sectionsFeedback: ResumeSection[] = [
    {
      sectionName: 'Contact Information',
      score: hasEmail && hasPhone ? 9 : (hasEmail || hasPhone ? 5 : 1),
      summary: hasEmail && hasPhone ? 'Good contact information' : 'Incomplete contact details',
      strengths: hasEmail && hasPhone ? ['Complete contact information'] : [],
      weaknesses: hasEmail && hasPhone ? [] : ['Missing email or phone number'],
      suggestions: hasEmail && hasPhone ? [] : ['Add complete contact information including email, phone, and LinkedIn'],
      examples: []
    },
    {
      sectionName: 'Professional Summary',
      score: hasSummary ? 8 : 2,
      summary: hasSummary ? 'Good summary present' : 'Missing professional summary',
      strengths: hasSummary ? ['Good summary'] : [],
      weaknesses: hasSummary ? [] : ['Missing summary'],
      suggestions: hasSummary ? ['Consider making your summary more achievement-oriented'] : ['Add a compelling professional summary'],
      examples: []
    },
    {
      sectionName: 'Skills Section',
      score: hasSkills ? 8 : 2,
      summary: hasSkills ? 'Skills section present' : 'Missing skills section',
      strengths: foundSkills.length > 10 ? ['Good variety of skills'] : [],
      weaknesses: foundSkills.length > 10 ? [] : ['Limited skills listed'],
      suggestions: foundSkills.length > 10 ? [] : ['Add more relevant skills with proficiency levels'],
      examples: []
    },
    {
      sectionName: 'Work Experience',
      score: hasExperience ? 9 : 2,
      summary: hasExperience ? 'Work experience section present' : 'Missing work experience',
      strengths: hasExperience ? ['Good work experience'] : [],
      weaknesses: hasExperience ? [] : ['Missing work experience'],
      suggestions: hasExperience ? ['Use more quantifiable achievements'] : ['Add detailed work experience with achievements'],
      examples: []
    },
    {
      sectionName: 'Education',
      score: hasEducation ? 7 : 2,
      summary: hasEducation ? 'Education section present' : 'Missing education details',
      strengths: hasEducation ? ['Good education background'] : [],
      weaknesses: hasEducation ? [] : ['Missing education details'],
      suggestions: hasEducation ? [] : ['Add your educational background'],
      examples: []
    },
    {
      sectionName: 'Design & Layout',
      score: hasBulletPoints && hasSections && hasConsistentIndent ? 9 : 3,
      summary: hasBulletPoints && hasSections && hasConsistentIndent ? 'Readable, well-structured layout' : 'Improve layout, use bullet points, clear sections, and consistent formatting',
      strengths: hasBulletPoints && hasSections && hasConsistentIndent ? ['Good layout'] : [],
      weaknesses: hasBulletPoints && hasSections && hasConsistentIndent ? [] : ['Poor layout'],
      suggestions: [
        ...(hasBulletPoints ? [] : ['Use bullet points for lists']),
        ...(hasSections ? [] : ['Use clear section headings']),
        ...(hasConsistentIndent ? [] : ['Use consistent indentation'])
      ],
      examples: []
    },
    {
      sectionName: 'Length',
      score: hasExcessiveLength ? 2 : (hasShortLength ? 1 : 8),
      summary: hasExcessiveLength ? 'Resume is too long' : (hasShortLength ? 'Resume is too short' : 'Good resume length'),
      strengths: hasExcessiveLength ? [] : (hasShortLength ? [] : ['Good length']),
      weaknesses: hasExcessiveLength ? ['Too long'] : (hasShortLength ? ['Too short'] : []),
      suggestions: [
        ...(hasExcessiveLength ? ['Reduce length to 1-2 pages'] : []),
        ...(hasShortLength ? ['Add more content, elaborate on your experience'] : [])
      ],
      examples: []
    }
  ];

  // --- Missing Sections ---
  const missingSections: string[] = [];
  if (!hasSummary) missingSections.push('Professional Summary');
  if (!hasSkills) missingSections.push('Skills Section');
  if (!hasExperience) missingSections.push('Work Experience');
  if (!hasEducation) missingSections.push('Education');
  if (!hasLinkedIn) missingSections.push('LinkedIn Profile');

  // --- Improvement Suggestions ---
  const improvement: string[] = [
    'Add more quantifiable achievements with metrics',
    'Tailor your resume to each job application',
    'Use action verbs to start bullet points',
    'Keep resume concise and focused on relevant experience',
    'Ensure consistent formatting throughout',
    ...(hasShortLength ? ['Expand your resume with more details and context'] : []),
    ...(hasExcessiveLength ? ['Trim unnecessary content and focus on relevance'] : [])
  ];

  // --- Strengths ---
  const strengths: string[] = [];
  if (score > 80) strengths.push('Excellent structure and content');
  if (score > 65) strengths.push('Well-structured resume');
  if (hasSkills && foundSkills.length > 8) strengths.push('Comprehensive skills section');
  if (hasExperience) strengths.push('Detailed work experience');
  if (hasEducation) strengths.push('Good education background');
  if (hasBulletPoints && hasSections && hasConsistentIndent) strengths.push('Professional layout and formatting');

  // --- Job Matches ---
  const jobMatches = generateJobMatches(foundSkills.map(s => s.skill));

  return {
    overallSummary: 'Resume analysis summary',
    overallScore: score,
    strengths,
    areasForImprovement: missingSections,
    sections: sectionsFeedback,
    keySkills: foundSkills,
    jobMatches,
    visualSuggestions: []
  };
};

// Generate job matches based on skills
const generateJobMatches = (skills: string[]): JobMatch[] => {
  const jobs: JobMatch[] = [
    {
      id: '1',
      title: 'Frontend Developer',
      company: 'TechStart Inc.',
      matchScore: 0,
      missingSkills: ['React', 'TypeScript', 'CSS']
    },
    {
      id: '2',
      title: 'Full Stack Engineer',
      company: 'GrowthTech',
      matchScore: 0,
      missingSkills: ['Node.js', 'React', 'MongoDB']
    },
    {
      id: '3',
      title: 'Backend Developer',
      company: 'DataFlow Systems',
      matchScore: 0,
      missingSkills: ['Node.js', 'Express', 'PostgreSQL']
    },
    {
      id: '4',
      title: 'DevOps Engineer',
      company: 'CloudScale',
      matchScore: 0,
      missingSkills: ['Docker', 'Kubernetes', 'AWS']
    },
    {
      id: '5',
      title: 'Mobile Developer',
      company: 'AppWorks',
      matchScore: 0,
      missingSkills: ['React Native', 'TypeScript', 'Redux']
    }
  ];

  // Calculate match score based on skills
  return jobs.map(job => {
    const requiredSkills = [...job.missingSkills];
    const matchedSkills = requiredSkills.filter(skill => 
      skills.some(userSkill => userSkill.toLowerCase() === skill.toLowerCase())
    );
    
    // Update missing skills based on what user has
    const updatedMissingSkills = requiredSkills.filter(skill => 
      !skills.some(userSkill => userSkill.toLowerCase() === skill.toLowerCase())
    );
    
    // Calculate match score
    const matchScore = Math.min(
      100, 
      Math.floor((matchedSkills.length / requiredSkills.length) * 100) + 
      Math.floor(Math.random() * 30) // Add some randomness for demo
    );
    
    return {
      ...job,
      matchScore,
      missingSkills: updatedMissingSkills
    };
  }).sort((a, b) => b.matchScore - a.matchScore); // Sort by match score
};

// Main function to process a resume file
export const processResume = async (file: ResumeFile): Promise<ResumeAnalysis> => {
  try {
    const content = await extractResumeContent(file);
    const analysis = await analyzeResume(content);
    return analysis;
  } catch (error) {
    console.error('Error processing resume:', error);
    throw new Error('Failed to process resume');
  }
};
