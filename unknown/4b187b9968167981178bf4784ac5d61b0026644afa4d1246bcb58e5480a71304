import { createClient } from '@supabase/supabase-js';
import Constants from 'expo-constants';

// Load Supabase URL & key from env or expo config
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || (Constants.expoConfig?.extra?.SUPABASE_URL as string) || '';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || (Constants.expoConfig?.extra?.SUPABASE_ANON_KEY as string) || '';

// Validate configuration
if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase configuration. Ensure EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY are set in your environment or app config.');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);
