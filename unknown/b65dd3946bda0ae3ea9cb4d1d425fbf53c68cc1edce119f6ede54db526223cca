import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch, TextInput, Alert } from 'react-native';
import { router } from 'expo-router';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import { supabase } from '@/lib/supabase';
import FontAwesome from '@expo/vector-icons/FontAwesome';

export default function CareerPreferencesOnboarding() {
  const { theme, user } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  // Career type options
  const careerTypes = [
    'Software Development',
    'Data Science',
    'UX/UI Design',
    'Product Management',
    'Marketing',
    'Sales',
    'Customer Service',
    'Finance',
    'Human Resources',
    'Operations',
    'Other'
  ];
  
  // Experience level options
  const experienceLevels = [
    'Entry Level',
    'Junior',
    'Mid-Level',
    'Senior',
    'Lead/Manager',
    'Executive'
  ];
  
  // Job priorities options
  const jobPriorities = [
    'Work-Life Balance',
    'Salary',
    'Career Growth',
    'Learning Opportunities',
    'Company Culture',
    'Remote Work',
    'Benefits',
    'Job Security',
    'Location',
    'Meaningful Work'
  ];
  
  // Urgency options
  const urgencyOptions = [
    'As soon as possible',
    'Within 1 month',
    'Within 3 months',
    'Within 6 months',
    'Just exploring options'
  ];
  
  // Interviews per week options
  const interviewsPerWeek = [1, 2, 3, 5, 'As many as possible'];
  
  // Form state
  const [selectedCareerTypes, setSelectedCareerTypes] = useState<string[]>([]);
  const [experienceLevel, setExperienceLevel] = useState('');
  const [selectedPriorities, setSelectedPriorities] = useState<string[]>([]);
  const [urgency, setUrgency] = useState('');
  const [interviews, setInterviews] = useState<number | string>(2);
  const [salaryMin, setSalaryMin] = useState('');
  const [salaryMax, setSalaryMax] = useState('');
  
  // Toggle selection in array
  const toggleSelection = (item: string, currentSelections: string[], setSelections: React.Dispatch<React.SetStateAction<string[]>>, maxSelections = 3) => {
    if (currentSelections.includes(item)) {
      setSelections(currentSelections.filter(i => i !== item));
    } else {
      if (currentSelections.length < maxSelections) {
        setSelections([...currentSelections, item]);
      } else {
        Alert.alert('Selection limit', `You can only select up to ${maxSelections} options.`);
      }
    }
  };
  
  // Save preferences to Supabase
  const savePreferences = async () => {
    if (!user) {
      setError('User not authenticated');
      return;
    }
    
    if (selectedCareerTypes.length === 0) {
      setError('Please select at least one career type');
      return;
    }
    
    if (!experienceLevel) {
      setError('Please select your experience level');
      return;
    }
    
    if (selectedPriorities.length === 0) {
      setError('Please select at least one job priority');
      return;
    }
    
    if (!urgency) {
      setError('Please select your job search urgency');
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      // Format salary expectations
      const salaryExpectations = {
        min: salaryMin ? parseInt(salaryMin, 10) : null,
        max: salaryMax ? parseInt(salaryMax, 10) : null
      };
      
      // Save to Supabase
      const { error: upsertError } = await supabase
        .from('career_preferences')
        .upsert({
          id: user.id,
          career_type: selectedCareerTypes,
          experience_level: experienceLevel,
          job_priorities: selectedPriorities,
          urgency: urgency,
          interviews_per_week: typeof interviews === 'string' ? null : interviews,
          salary_expectations: salaryExpectations
        });
      
      if (upsertError) throw upsertError;
      
      // Get user email from auth
      const { data: { user: currentUser } } = await supabase.auth.getUser();
      const userEmail = currentUser?.email;
      
      // Update user profile with email and mark onboarding as complete
      const { error: profileError } = await supabase
        .from('profiles')
        .update({ 
          onboarding_completed: true,
          email: userEmail,
          user_type: 'job_seeker' 
        })
        .eq('id', user.id);
      
      if (profileError) throw profileError;
      
      // Navigate to home after successful save
      router.replace('/(tabs)/home');
      
    } catch (error) {
      console.error('Error saving preferences:', error);
      setError('Failed to save your preferences. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: themeColors.background }]}
      contentContainerStyle={{ paddingBottom: 40 }}
    >
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>Let's personalize your job search</Text>
        <Text style={[styles.headerSubtitle, { color: themeColors.textSecondary }]}>
          Help us understand what you're looking for so we can find the right opportunities for you
        </Text>
      </View>
      
      {/* Error message */}
      {error ? (
        <Text style={styles.error}>{error}</Text>
      ) : null}
      
      {/* Career Types Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>What type of career interests you?</Text>
        <Text style={[styles.sectionSubtitle, { color: themeColors.textSecondary }]}>Select up to 3 options</Text>
        
        <View style={styles.optionsGrid}>
          {careerTypes.map((type) => (
            <TouchableOpacity
              key={type}
              style={[
                styles.optionButton,
                selectedCareerTypes.includes(type) && { backgroundColor: themeColors.primary },
                { borderColor: themeColors.border }
              ]}
              onPress={() => toggleSelection(type, selectedCareerTypes, setSelectedCareerTypes)}
            >
              <Text
                style={[
                  styles.optionText,
                  selectedCareerTypes.includes(type) && { color: '#fff' },
                  !selectedCareerTypes.includes(type) && { color: themeColors.text }
                ]}
              >
                {type}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      {/* Experience Level Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>What's your experience level?</Text>
        
        <View style={styles.optionsRow}>
          {experienceLevels.map((level) => (
            <TouchableOpacity
              key={level}
              style={[
                styles.pillButton,
                experienceLevel === level && { backgroundColor: themeColors.primary },
                { borderColor: themeColors.border }
              ]}
              onPress={() => setExperienceLevel(level)}
            >
              <Text
                style={[
                  styles.pillText,
                  experienceLevel === level && { color: '#fff' },
                  experienceLevel !== level && { color: themeColors.text }
                ]}
              >
                {level}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      {/* Job Priorities Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>What's most important to you in a job?</Text>
        <Text style={[styles.sectionSubtitle, { color: themeColors.textSecondary }]}>Select up to 3 options</Text>
        
        <View style={styles.optionsGrid}>
          {jobPriorities.map((priority) => (
            <TouchableOpacity
              key={priority}
              style={[
                styles.optionButton,
                selectedPriorities.includes(priority) && { backgroundColor: themeColors.primary },
                { borderColor: themeColors.border }
              ]}
              onPress={() => toggleSelection(priority, selectedPriorities, setSelectedPriorities)}
            >
              <Text
                style={[
                  styles.optionText,
                  selectedPriorities.includes(priority) && { color: '#fff' },
                  !selectedPriorities.includes(priority) && { color: themeColors.text }
                ]}
              >
                {priority}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      {/* Urgency Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>How soon do you need a new job?</Text>
        
        <View style={styles.verticalOptions}>
          {urgencyOptions.map((option) => (
            <TouchableOpacity
              key={option}
              style={[
                styles.verticalOption,
                urgency === option && { backgroundColor: themeColors.backgroundSecondary },
                { borderColor: themeColors.border }
              ]}
              onPress={() => setUrgency(option)}
            >
              <View style={styles.radioContainer}>
                <View 
                  style={[
                    styles.radioOuter,
                    { borderColor: urgency === option ? themeColors.primary : themeColors.border }
                  ]}
                >
                  {urgency === option && (
                    <View style={[styles.radioInner, { backgroundColor: themeColors.primary }]} />
                  )}
                </View>
              </View>
              <Text style={[styles.verticalOptionText, { color: themeColors.text }]}>{option}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      {/* Interviews Per Week Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>How many interviews would you like per week?</Text>
        
        <View style={styles.optionsRow}>
          {interviewsPerWeek.map((num) => (
            <TouchableOpacity
              key={num.toString()}
              style={[
                styles.pillButton,
                interviews === num && { backgroundColor: themeColors.primary },
                { borderColor: themeColors.border }
              ]}
              onPress={() => setInterviews(num)}
            >
              <Text
                style={[
                  styles.pillText,
                  interviews === num && { color: '#fff' },
                  interviews !== num && { color: themeColors.text }
                ]}
              >
                {num.toString()}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      {/* Salary Expectations Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>What are your salary expectations?</Text>
        
        <View style={styles.salaryContainer}>
          <View style={styles.salaryField}>
            <Text style={[styles.salaryLabel, { color: themeColors.textSecondary }]}>Minimum ($)</Text>
            <TextInput
              style={[styles.salaryInput, { borderColor: themeColors.border, color: themeColors.text }]}
              keyboardType="numeric"
              value={salaryMin}
              onChangeText={setSalaryMin}
              placeholder="Minimum"
              placeholderTextColor={themeColors.textSecondary}
            />
          </View>
          
          <View style={styles.salaryField}>
            <Text style={[styles.salaryLabel, { color: themeColors.textSecondary }]}>Maximum ($)</Text>
            <TextInput
              style={[styles.salaryInput, { borderColor: themeColors.border, color: themeColors.text }]}
              keyboardType="numeric"
              value={salaryMax}
              onChangeText={setSalaryMax}
              placeholder="Maximum"
              placeholderTextColor={themeColors.textSecondary}
            />
          </View>
        </View>
      </View>
      
      {/* Bottom Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.skipButton, { borderColor: themeColors.border }]}
          onPress={() => router.replace('/(tabs)/home')}
        >
          <Text style={[styles.skipButtonText, { color: themeColors.text }]}>Skip for now</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.submitButton, { backgroundColor: themeColors.primary }]}
          onPress={savePreferences}
          disabled={loading}
        >
          <Text style={styles.submitButtonText}>
            {loading ? 'Saving...' : 'Save & Continue'}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
  error: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  section: {
    padding: 20,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    marginBottom: 16,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -5,
  },
  optionButton: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    margin: 5,
    minWidth: '30%',
    alignItems: 'center',
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  optionsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -5,
  },
  pillButton: {
    borderWidth: 1,
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    margin: 5,
    alignItems: 'center',
  },
  pillText: {
    fontSize: 14,
    fontWeight: '500',
  },
  verticalOptions: {
    marginTop: 8,
  },
  verticalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  radioContainer: {
    marginRight: 12,
  },
  radioOuter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  verticalOptionText: {
    fontSize: 16,
  },
  salaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  salaryField: {
    flex: 1,
    marginHorizontal: 5,
  },
  salaryLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  salaryInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    paddingBottom: 40,
  },
  skipButton: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 14,
    alignItems: 'center',
    marginRight: 8,
  },
  skipButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  submitButton: {
    flex: 2,
    borderRadius: 8,
    padding: 14,
    alignItems: 'center',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});
