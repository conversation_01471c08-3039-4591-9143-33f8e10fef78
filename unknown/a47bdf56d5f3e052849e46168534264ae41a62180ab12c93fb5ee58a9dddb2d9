#!/usr/bin/env node

/**
 * Test Database Connection and Create Sample Preferences
 * This script tests the database connection and creates sample user preferences
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const SUPABASE_URL = 'https://ubueawlkwlvgzxcslats.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVidWVhd2xrd2x2Z3p4Y3NsYXRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0MzgzMTIsImV4cCI6MjA2MTAxNDMxMn0._YMssGKgq17C5XMkPnN5rq5Zhu_u4WsvNiveYIPd4lg';

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testDatabase() {
  try {
    console.log('🧪 Testing Database Connection...\n');

    // Test 1: Check if table exists
    console.log('1️⃣  Testing table existence...');
    const { data: tableCheck, error: tableError } = await supabase
      .from('user_job_preferences')
      .select('id')
      .limit(1);

    if (tableError && tableError.code === 'PGRST116') {
      console.error('❌ Table does not exist:', tableError.message);
      return;
    } else if (tableError) {
      console.error('❌ Unexpected error:', tableError);
      return;
    }

    console.log('✅ user_job_preferences table exists and is accessible');

    // Test 2: Try to insert sample preferences (this will fail without auth, but that's expected)
    console.log('\n2️⃣  Testing table structure...');
    
    const samplePreferences = {
      user_id: '7a8234ac-7c0c-434c-9e8a-fc4b06e7ac38', // Your user ID from the logs
      preferred_locations: ['San Francisco', 'Remote', 'New York'],
      max_commute_distance: 30,
      remote_work_preference: 'preferred',
      willing_to_relocate: false,
      preferred_job_types: ['Full-time'],
      preferred_industries: ['Technology', 'Software'],
      preferred_company_sizes: ['startup', 'medium'],
      experience_level: 'senior',
      preferred_roles: ['Software Engineer', 'Developer', 'Full Stack Developer'],
      min_salary: 100000,
      max_salary: 180000,
      salary_currency: 'USD',
      salary_negotiable: true,
      preferred_schedule: 'flexible',
      location_weight: 0.25,
      salary_weight: 0.30,
      role_weight: 0.25,
      company_weight: 0.20,
      auto_learn_from_swipes: true
    };

    // This will fail due to RLS, but it tests the table structure
    const { data: insertData, error: insertError } = await supabase
      .from('user_job_preferences')
      .insert(samplePreferences);

    if (insertError) {
      if (insertError.code === 'PGRST301' || insertError.message.includes('RLS')) {
        console.log('✅ Table structure is correct (RLS is working as expected)');
        console.log('   Note: Insert failed due to RLS - this is normal without authentication');
      } else {
        console.error('❌ Table structure issue:', insertError);
        return;
      }
    } else {
      console.log('✅ Sample preferences inserted successfully');
    }

    // Test 3: Check table schema
    console.log('\n3️⃣  Checking table schema...');
    const { data: schemaData, error: schemaError } = await supabase.rpc('get_table_schema', {
      table_name: 'user_job_preferences'
    });

    if (schemaError) {
      console.log('⚠️  Could not fetch schema details (this is normal)');
    }

    console.log('\n🎉 Database tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ user_job_preferences table exists');
    console.log('   ✅ Table structure is correct');
    console.log('   ✅ RLS policies are active');
    console.log('   ✅ Ready for use with authenticated users');

    console.log('\n🚀 Next steps:');
    console.log('   1. Restart your React Native app');
    console.log('   2. The "relation does not exist" error should be gone');
    console.log('   3. Users can now set job preferences');
    console.log('   4. Personalized recommendations will work');

  } catch (error) {
    console.error('❌ Database test failed:', error);
  }
}

// Run the test
if (require.main === module) {
  testDatabase();
}

module.exports = { testDatabase };
