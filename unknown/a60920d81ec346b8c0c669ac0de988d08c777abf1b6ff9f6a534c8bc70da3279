// Theme color constants for the app

// Jobbify brand colors
const jobbifyBlue = '#0078E7'; // Primary blue from the logo
const jobbifyLightBlue = '#4DA3FF'; // Lighter blue for accents
const jobbifyDarkBlue = '#005BB8'; // Darker blue for hover states
const jobbifyBlack = '#1A1A1A'; // Dark black for text
const jobbifyDarkGray = '#333333'; // Dark gray for secondary text
const jobbifyMediumGray = '#666666'; // Medium gray for tertiary text
const jobbifyLightGray = '#EEEEEE'; // Light gray for borders
const jobbifyWhite = '#FFFFFF'; // White

export interface ThemeColors {
  // Base colors
  background: string;
  card: string;
  cardSecondary: string;  // secondary card background
  text: string;
  textSecondary: string;
  border: string;
  tint: string;
  error: string;
  success: string;
  warning: string;
  info: string;
  
  // Component specific
  headerBackground: string;
  tabBackground: string;
  tabIconDefault: string;
  tabIconSelected: string;
  cardGradient: readonly [string, string, string]; 
  applyButtonColor: string;
  passButtonColor: string;
  infoButtonColor: string;
  searchBackground: string;
  statusBackground: string;
  modalBackdrop: string;
  backgroundSecondary: string;  // secondary background for modals/buttons
  
  // Additional theme colors
  primary?: string;
  danger?: string;
}

export const LightTheme: ThemeColors = {
  // Base colors
  background: '#FFFFFF',
  card: '#FFFFFF',
  cardSecondary: '#F7F7F7',
  text: '#111111',
  textSecondary: '#888888',
  border: '#E0E0E0',
  tint: '#111111',
  error: '#E53935',
  success: '#43A047',
  warning: '#FBC02D',
  info: '#1976D2',
  
  // Component specific
  headerBackground: '#FFFFFF',
  tabBackground: '#FFFFFF',
  tabIconDefault: '#888888',
  tabIconSelected: '#111111',
  cardGradient: ['transparent', 'rgba(0,0,0,0.7)', 'rgba(0,0,0,0.9)'] as const,
  applyButtonColor: '#111111',
  passButtonColor: '#E53935',
  infoButtonColor: '#1976D2',
  searchBackground: '#F4F4F4',
  statusBackground: '#F7F7F7',
  modalBackdrop: 'rgba(0,0,0,0.08)',
  backgroundSecondary: '#F4F4F4',
};

export const DarkTheme: ThemeColors = {
  // Base colors
  background: '#000000',
  card: '#111111',
  cardSecondary: '#181818',
  text: '#FFFFFF',
  textSecondary: '#B0B0B0',
  border: '#222222',
  tint: '#FFFFFF',
  error: '#FF5252',
  success: '#69F0AE',
  warning: '#FFD740',
  info: '#40C4FF',
  
  // Component specific
  headerBackground: '#000000',
  tabBackground: '#000000',
  tabIconDefault: '#B0B0B0',
  tabIconSelected: '#FFFFFF',
  cardGradient: ['transparent', 'rgba(255,255,255,0.7)', 'rgba(255,255,255,0.9)'] as const,
  applyButtonColor: '#FFFFFF',
  passButtonColor: '#FF5252',
  infoButtonColor: '#40C4FF',
  searchBackground: '#181818',
  statusBackground: '#181818',
  modalBackdrop: 'rgba(255,255,255,0.08)',
  backgroundSecondary: '#181818',
};
