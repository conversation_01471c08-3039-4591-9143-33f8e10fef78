import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  ActivityIndicator
} from 'react-native';
import { Stack, router } from 'expo-router';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import { 
  getUserJobPreferences,
  saveUserJobPreferences, 
  getDefaultUserPreferences,
  UserJobPreferences 
} from '@/services/jobRecommendationService';

export default function JobPreferencesModal() {
  const { theme, user } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [preferences, setPreferences] = useState<UserJobPreferences | null>(null);

  useEffect(() => {
    loadUserPreferences();
  }, [user?.id]);

  const loadUserPreferences = async () => {
    if (!user?.id) {
      setError('User not found');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const userPrefs = await getUserJobPreferences(user.id);
      
      if (userPrefs) {
        setPreferences(userPrefs);
      } else {
        // No preferences found, use defaults
        setPreferences(getDefaultUserPreferences(user.id));
      }
    } catch (error) {
      console.error('Error loading preferences:', error);
      setError('Failed to load preferences');
    } finally {
      setLoading(false);
    }
  };

  const handleSavePreferences = async () => {
    if (!preferences || !user?.id) {
      setError('Unable to save preferences');
      return;
    }

    setSaving(true);
    setError('');

    try {
      const success = await saveUserJobPreferences(preferences);
      
      if (success) {
        Alert.alert(
          'Preferences Updated!',
          'Your job preferences have been saved successfully.',
          [
            {
              text: 'OK',
              onPress: () => router.back()
            }
          ]
        );
      } else {
        setError('Failed to save preferences. Please try again.');
      }
    } catch (error) {
      console.error('Error saving preferences:', error);
      setError('An error occurred while saving your preferences.');
    } finally {
      setSaving(false);
    }
  };

  const resetToDefaults = () => {
    Alert.alert(
      'Reset Preferences',
      'Are you sure you want to reset all preferences to default values?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            if (user?.id) {
              setPreferences(getDefaultUserPreferences(user.id));
            }
          }
        }
      ]
    );
  };

  const renderPreferenceSummary = () => {
    if (!preferences) return null;

    return (
      <View style={styles.summaryContainer}>
        <Text style={[styles.summaryTitle, { color: themeColors.text }]}>
          Current Preferences Summary
        </Text>
        
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: themeColors.textSecondary }]}>
            Remote Work:
          </Text>
          <Text style={[styles.summaryValue, { color: themeColors.text }]}>
            {preferences.remote_work_preference.charAt(0).toUpperCase() + 
             preferences.remote_work_preference.slice(1)}
          </Text>
        </View>

        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: themeColors.textSecondary }]}>
            Experience Level:
          </Text>
          <Text style={[styles.summaryValue, { color: themeColors.text }]}>
            {preferences.experience_level.charAt(0).toUpperCase() + 
             preferences.experience_level.slice(1)}
          </Text>
        </View>

        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: themeColors.textSecondary }]}>
            Preferred Locations:
          </Text>
          <Text style={[styles.summaryValue, { color: themeColors.text }]}>
            {preferences.preferred_locations.length > 0 
              ? preferences.preferred_locations.join(', ')
              : 'Any location'}
          </Text>
        </View>

        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: themeColors.textSecondary }]}>
            Job Types:
          </Text>
          <Text style={[styles.summaryValue, { color: themeColors.text }]}>
            {preferences.preferred_job_types.length > 0 
              ? preferences.preferred_job_types.join(', ')
              : 'Any job type'}
          </Text>
        </View>

        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: themeColors.textSecondary }]}>
            Preferred Roles:
          </Text>
          <Text style={[styles.summaryValue, { color: themeColors.text }]}>
            {preferences.preferred_roles.length > 0 
              ? preferences.preferred_roles.join(', ')
              : 'Any role'}
          </Text>
        </View>

        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: themeColors.textSecondary }]}>
            Salary Range:
          </Text>
          <Text style={[styles.summaryValue, { color: themeColors.text }]}>
            {preferences.min_salary || preferences.max_salary
              ? `$${preferences.min_salary?.toLocaleString() || '0'} - $${preferences.max_salary?.toLocaleString() || '∞'}`
              : 'No preference set'}
          </Text>
        </View>

        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: themeColors.textSecondary }]}>
            Preference Weights:
          </Text>
          <Text style={[styles.summaryValue, { color: themeColors.text }]}>
            Location: {Math.round(preferences.location_weight * 100)}%, 
            Salary: {Math.round(preferences.salary_weight * 100)}%, 
            Role: {Math.round(preferences.role_weight * 100)}%, 
            Company: {Math.round(preferences.company_weight * 100)}%
          </Text>
        </View>

        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: themeColors.textSecondary }]}>
            Auto-learn from swipes:
          </Text>
          <Text style={[styles.summaryValue, { color: themeColors.text }]}>
            {preferences.auto_learn_from_swipes ? 'Enabled' : 'Disabled'}
          </Text>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
        <Stack.Screen
          options={{
            title: 'Job Preferences',
            headerStyle: { backgroundColor: themeColors.headerBackground },
            headerTintColor: themeColors.tint,
            headerTitleStyle: { color: themeColors.text },
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.tint} />
          <Text style={[styles.loadingText, { color: themeColors.text }]}>
            Loading preferences...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
      <Stack.Screen
        options={{
          title: 'Job Preferences',
          headerStyle: { backgroundColor: themeColors.headerBackground },
          headerTintColor: themeColors.tint,
          headerTitleStyle: { color: themeColors.text },
        }}
      />
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <Text style={[styles.title, { color: themeColors.text }]}>
            Manage Your Job Preferences
          </Text>
          <Text style={[styles.description, { color: themeColors.textSecondary }]}>
            Your preferences help us show you the most relevant job recommendations. 
            You can update these anytime to improve your job search experience.
          </Text>

          {renderPreferenceSummary()}

          {error ? (
            <Text style={[styles.errorText, { color: themeColors.error }]}>
              {error}
            </Text>
          ) : null}
        </View>
      </ScrollView>

      <View style={[styles.buttonContainer, { backgroundColor: themeColors.background }]}>
        <TouchableOpacity
          style={[styles.secondaryButton, { borderColor: themeColors.border }]}
          onPress={resetToDefaults}
        >
          <Text style={[styles.secondaryButtonText, { color: themeColors.text }]}>
            Reset to Defaults
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.editButton, { backgroundColor: themeColors.tint }]}
          onPress={() => router.push('/(onboarding)/job-preferences')}
        >
          <Text style={styles.editButtonText}>
            Edit Preferences
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 30,
  },
  summaryContainer: {
    gap: 16,
    marginBottom: 30,
  },
  summaryTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },
  summaryItem: {
    gap: 4,
  },
  summaryLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  summaryValue: {
    fontSize: 16,
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    gap: 12,
  },
  editButton: {
    flex: 1,
    height: 50,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  editButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    flex: 1,
    height: 50,
    borderRadius: 12,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 10,
  },
});
