-- Create profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT,
  email TEXT UNIQUE,
  user_type TEXT NOT NULL CHECK (user_type IN ('job_seeker', 'service_provider')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create job_seeker_profiles table
CREATE TABLE IF NOT EXISTS job_seeker_profiles (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT,
  bio TEXT,
  phone TEXT,
  location TEXT,
  photo_url TEXT,
  resume_url TEXT,
  preferences JSONB DEFAULT '{}'::JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_job_preferences table for personalized filtering
CREATE TABLE IF NOT EXISTS user_job_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,

  -- Location preferences
  preferred_locations TEXT[] DEFAULT '{}',
  max_commute_distance INTEGER DEFAULT 50, -- in miles/km
  remote_work_preference TEXT CHECK (remote_work_preference IN ('required', 'preferred', 'acceptable', 'not_preferred')) DEFAULT 'preferred',
  willing_to_relocate BOOLEAN DEFAULT FALSE,

  -- Job type preferences
  preferred_job_types TEXT[] DEFAULT '{}', -- ['Full-time', 'Part-time', 'Contract', 'Freelance']
  preferred_industries TEXT[] DEFAULT '{}',
  preferred_company_sizes TEXT[] DEFAULT '{}', -- ['startup', 'small', 'medium', 'large', 'enterprise']

  -- Experience and role preferences
  experience_level TEXT CHECK (experience_level IN ('entry', 'junior', 'mid', 'senior', 'lead', 'executive')) DEFAULT 'mid',
  preferred_roles TEXT[] DEFAULT '{}',

  -- Compensation preferences
  min_salary INTEGER,
  max_salary INTEGER,
  salary_currency TEXT DEFAULT 'USD',
  salary_negotiable BOOLEAN DEFAULT TRUE,

  -- Work schedule preferences
  preferred_schedule TEXT CHECK (preferred_schedule IN ('flexible', 'standard', 'shift_work', 'weekends_ok')) DEFAULT 'flexible',

  -- Preference weights (for scoring algorithm)
  location_weight DECIMAL(3,2) DEFAULT 0.25,
  salary_weight DECIMAL(3,2) DEFAULT 0.30,
  role_weight DECIMAL(3,2) DEFAULT 0.25,
  company_weight DECIMAL(3,2) DEFAULT 0.20,

  -- Learning preferences
  auto_learn_from_swipes BOOLEAN DEFAULT TRUE,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  UNIQUE(user_id)
);

-- Create service_provider_profiles table
CREATE TABLE IF NOT EXISTS service_provider_profiles (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  name TEXT,
  bio TEXT,
  phone TEXT,
  location TEXT,
  photo_url TEXT,
  service_categories TEXT[],
  service_description TEXT,
  pricing_model TEXT CHECK (pricing_model IN ('hourly', 'fixed', 'estimate')),
  hourly_rate DECIMAL(10, 2),
  fixed_price DECIMAL(10, 2),
  estimate_min DECIMAL(10, 2),
  estimate_max DECIMAL(10, 2),
  service_area_radius DECIMAL(10, 2),
  service_area_center TEXT,
  availability JSONB DEFAULT '{}'::JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create experiences table
CREATE TABLE IF NOT EXISTS experiences (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  company TEXT NOT NULL,
  start_date TEXT,
  end_date TEXT,
  description TEXT,
  is_current BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create educations table
CREATE TABLE IF NOT EXISTS educations (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  school TEXT NOT NULL,
  degree TEXT NOT NULL,
  field TEXT,
  start_date TEXT,
  end_date TEXT,
  description TEXT,
  is_current BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_skills table
CREATE TABLE IF NOT EXISTS user_skills (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  skill_name TEXT NOT NULL,
  proficiency_level TEXT CHECK (proficiency_level IN ('Beginner', 'Intermediate', 'Advanced', 'Expert')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create provider_certifications table
CREATE TABLE IF NOT EXISTS provider_certifications (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  issuer TEXT NOT NULL,
  issue_date TEXT,
  file_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create jobs table
CREATE TABLE IF NOT EXISTS jobs (
  id SERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  company TEXT NOT NULL,
  location TEXT,
  description TEXT,
  requirements TEXT[],
  qualifications TEXT[],
  salary_min DECIMAL(10, 2),
  salary_max DECIMAL(10, 2),
  job_type TEXT CHECK (job_type IN ('Full-time', 'Part-time', 'Contract', 'Internship', 'Temporary')),
  remote BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create job_skills table
CREATE TABLE IF NOT EXISTS job_skills (
  id SERIAL PRIMARY KEY,
  job_id INTEGER REFERENCES jobs(id) ON DELETE CASCADE,
  skill_name TEXT NOT NULL,
  is_required BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create swipes table (enhanced for learning algorithm)
CREATE TABLE IF NOT EXISTS swipes (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  job_id INTEGER REFERENCES jobs(id) ON DELETE CASCADE,
  direction TEXT CHECK (direction IN ('left', 'right')),

  -- Additional data for learning algorithm
  job_title TEXT,
  job_company TEXT,
  job_location TEXT,
  job_salary_min INTEGER,
  job_salary_max INTEGER,
  job_type TEXT,
  job_remote BOOLEAN,
  job_tags TEXT[],

  -- Matching score when swiped (for algorithm improvement)
  match_score DECIMAL(5,2),

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, job_id)
);

-- Create job_recommendations table for tracking algorithm performance
CREATE TABLE IF NOT EXISTS job_recommendations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  job_id INTEGER REFERENCES jobs(id) ON DELETE CASCADE,

  -- Recommendation scoring details
  overall_score DECIMAL(5,2) NOT NULL,
  location_score DECIMAL(5,2),
  salary_score DECIMAL(5,2),
  role_score DECIMAL(5,2),
  company_score DECIMAL(5,2),

  -- Recommendation metadata
  algorithm_version TEXT DEFAULT 'v1.0',
  recommendation_reason TEXT,

  -- User interaction tracking
  was_viewed BOOLEAN DEFAULT FALSE,
  was_swiped BOOLEAN DEFAULT FALSE,
  swipe_direction TEXT CHECK (swipe_direction IN ('left', 'right')),
  was_applied BOOLEAN DEFAULT FALSE,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  UNIQUE(user_id, job_id)
);

-- Create matches table
CREATE TABLE IF NOT EXISTS matches (
  id SERIAL PRIMARY KEY,
  profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  job_id TEXT NOT NULL,
  job_title TEXT,
  job_company TEXT,
  status TEXT CHECK (status IN ('pending', 'accepted', 'rejected', 'archived', 'applying')) DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(profile_id, job_id)
);

-- Create messages table
CREATE TABLE IF NOT EXISTS messages (
  id SERIAL PRIMARY KEY,
  match_id INTEGER REFERENCES matches(id) ON DELETE CASCADE,
  sender_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create gigs table
CREATE TABLE IF NOT EXISTS gigs (
  id SERIAL PRIMARY KEY,
  provider_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  location TEXT,
  price DECIMAL(10, 2),
  price_type TEXT CHECK (price_type IN ('hourly', 'fixed', 'estimate')),
  category TEXT,
  status TEXT CHECK (status IN ('active', 'completed', 'cancelled')) DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create gig_requests table
CREATE TABLE IF NOT EXISTS gig_requests (
  id SERIAL PRIMARY KEY,
  gig_id INTEGER REFERENCES gigs(id) ON DELETE CASCADE,
  requester_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  status TEXT CHECK (status IN ('pending', 'accepted', 'rejected', 'completed', 'cancelled')) DEFAULT 'pending',
  message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create bookmarks table
CREATE TABLE IF NOT EXISTS bookmarks (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  item_type TEXT CHECK (item_type IN ('job', 'gig', 'profile')),
  item_id INTEGER NOT NULL,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, item_type, item_id)
);

-- Create external_applications table
CREATE TABLE IF NOT EXISTS external_applications (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  job_title TEXT NOT NULL,
  company TEXT NOT NULL,
  application_date DATE NOT NULL,
  status TEXT CHECK (status IN ('applied', 'interviewing', 'offered', 'rejected', 'accepted', 'withdrawn')) DEFAULT 'applied',
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies

-- Profiles table policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own profile"
  ON profiles FOR SELECT
  USING (auth.uid() = id);

-- User job preferences policies
ALTER TABLE user_job_preferences ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own job preferences"
  ON user_job_preferences FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own job preferences"
  ON user_job_preferences FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own job preferences"
  ON user_job_preferences FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own job preferences"
  ON user_job_preferences FOR DELETE
  USING (auth.uid() = user_id);

-- Job recommendations policies
ALTER TABLE job_recommendations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own job recommendations"
  ON job_recommendations FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "System can insert job recommendations"
  ON job_recommendations FOR INSERT
  WITH CHECK (true); -- Allow system to insert recommendations

CREATE POLICY "System can update job recommendations"
  ON job_recommendations FOR UPDATE
  USING (true); -- Allow system to update recommendation tracking

CREATE POLICY "Users can update their own profile" 
  ON profiles FOR UPDATE 
  USING (auth.uid() = id);

-- Job seeker profiles table policies
ALTER TABLE job_seeker_profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Job seekers can view their own profile" 
  ON job_seeker_profiles FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Job seekers can update their own profile" 
  ON job_seeker_profiles FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Job seekers can insert their own profile" 
  ON job_seeker_profiles FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Service provider profiles table policies
ALTER TABLE service_provider_profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Service providers can view their own profile" 
  ON service_provider_profiles FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Service providers can update their own profile" 
  ON service_provider_profiles FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Service providers can insert their own profile" 
  ON service_provider_profiles FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Experiences table policies
ALTER TABLE experiences ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own experiences" 
  ON experiences FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own experiences" 
  ON experiences FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own experiences" 
  ON experiences FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own experiences" 
  ON experiences FOR DELETE 
  USING (auth.uid() = user_id);

-- Educations table policies
ALTER TABLE educations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own educations" 
  ON educations FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own educations" 
  ON educations FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own educations" 
  ON educations FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own educations" 
  ON educations FOR DELETE 
  USING (auth.uid() = user_id);

-- User skills table policies
ALTER TABLE user_skills ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own skills" 
  ON user_skills FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own skills" 
  ON user_skills FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own skills" 
  ON user_skills FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own skills" 
  ON user_skills FOR DELETE 
  USING (auth.uid() = user_id);

-- Provider certifications table policies
ALTER TABLE provider_certifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Providers can view their own certifications" 
  ON provider_certifications FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Providers can update their own certifications" 
  ON provider_certifications FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Providers can insert their own certifications" 
  ON provider_certifications FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Providers can delete their own certifications" 
  ON provider_certifications FOR DELETE 
  USING (auth.uid() = user_id);

-- Create storage buckets
-- Note: This would typically be done in the Supabase dashboard

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_profiles_modtime
BEFORE UPDATE ON profiles
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_job_seeker_profiles_modtime
BEFORE UPDATE ON job_seeker_profiles
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_service_provider_profiles_modtime
BEFORE UPDATE ON service_provider_profiles
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_experiences_modtime
BEFORE UPDATE ON experiences
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_educations_modtime
BEFORE UPDATE ON educations
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_user_skills_modtime
BEFORE UPDATE ON user_skills
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_matches_modtime
BEFORE UPDATE ON matches
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_gigs_modtime
BEFORE UPDATE ON gigs
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_gig_requests_modtime
BEFORE UPDATE ON gig_requests
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_external_applications_modtime
BEFORE UPDATE ON external_applications
FOR EACH ROW EXECUTE FUNCTION update_modified_column();