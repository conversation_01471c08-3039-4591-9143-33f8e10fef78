import { useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { router } from 'expo-router';
import { useAppContext } from '@/context/AppContext';

/**
 * Hook to enforce authentication and redirect as needed
 */
export function useAuthCheck(options?: { redirectTo?: string; redirectIfAuthed?: boolean }) {
  const { setUser } = useAppContext();
  
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Get the current session
        const { data: { session } } = await supabase.auth.getSession();
        
        // Update user in context if we have a session
        if (session?.user) {
          // Format the user object to match your app's User type
          setUser({
            id: session.user.id,
            name: session.user.user_metadata?.name || 'User',
            email: session.user.email || '',
            avatar: session.user.user_metadata?.avatar_url || '',
            skills: [],
            experience: [],
          });
          
          // If we should redirect authenticated users away from auth pages
          if (options?.redirectIfAuthed) {
            // Add a small delay to ensure root layout is mounted
            setTimeout(() => {
              try {
                router.replace('/(tabs)');
              } catch (navError) {
                console.error('Navigation error:', navError);
              }
            }, 100);
          }
        } else {
          // No session, set user to null
          setUser(null);
          
          // If we should redirect unauthenticated users to login
          if (!options?.redirectIfAuthed) {
            // Add a small delay to ensure root layout is mounted
            setTimeout(() => {
              try {
                if (options?.redirectTo) {
                  router.replace(options.redirectTo);
                } else {
                  router.replace('/(auth)/login');
                }
              } catch (navError) {
                console.error('Navigation error:', navError);
              }
            }, 100);
          }
        }
      } catch (error) {
        console.error('Error checking auth:', error);
        setUser(null);
      }
    };
    
    checkAuth();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (_event, session) => {
        if (!session && !options?.redirectIfAuthed) {
          // User has been logged out, redirect to login
          if (options?.redirectTo) {
            router.replace(options.redirectTo);
          } else {
            router.replace('/(auth)/login');
          }
        } else if (session && options?.redirectIfAuthed) {
          // User has logged in, redirect away from auth pages
          router.replace('/(tabs)');
        }
      }
    );
    
    return () => {
      subscription.unsubscribe();
    };
  }, []);
} 