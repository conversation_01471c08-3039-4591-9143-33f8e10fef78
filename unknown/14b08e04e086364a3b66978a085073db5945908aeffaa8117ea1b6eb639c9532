// Import polyfills first to fix WebCrypto API warning
import 'react-native-get-random-values';

import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useFonts } from 'expo-font';
import { Slot, Stack, useSegments, useRouter } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { useEffect, useState, useCallback } from 'react';
import { AppProvider, useAppContext } from '../context/AppContext';
import { Text, View, ActivityIndicator } from 'react-native';
import { supabase } from '../lib/supabase';
import { Session } from '@supabase/supabase-js';
import React from 'react';

// Prevent the splash screen from auto-hiding before asset loading is complete
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded, error] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
    PoppinsRegular: require('../assets/fonts/Poppins-Regular.ttf'),
    PoppinsMedium: require('../assets/fonts/Poppins-Medium.ttf'),
    PoppinsBold: require('../assets/fonts/Poppins-Bold.ttf'),
    ...FontAwesome.font,
  });
  const [fontTimeout, setFontTimeout] = useState(false);

  useEffect(() => {
    if (error) throw error;
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  // Add a timeout for font loading
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!loaded) {
        setFontTimeout(true);
        console.warn('Font loading timeout!');
      }
    }, 3000);
    return () => clearTimeout(timer);
  }, [loaded]);

  if (!loaded && !fontTimeout) {
    return null;
  }

  if (fontTimeout && !loaded) {
    return (
      <Text style={{ color: 'red', marginTop: 100, fontSize: 20 }}>
        Font loading failed or is taking too long!
      </Text>
    );
  }

  return (
    <AppProvider>
      <RootLayoutNav />
    </AppProvider>
  );
}

function RootLayoutNav() {
  const { user, setUser } = useAppContext();
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const sessionCheckedRef = React.useRef(false);

  // Memoize the user setter to prevent unnecessary re-renders
  const handleUserUpdate = useCallback((sessionUser: any) => {
    if (!sessionUser) {
      setUser(null);
      return;
    }
    
    setUser({
      id: sessionUser.id,
      name: sessionUser.user_metadata?.name || 'User',
      email: sessionUser.email || '',
      avatar: sessionUser.user_metadata?.avatar_url || '',
      skills: [],
      experience: [],
    });
  }, [setUser]);

  // Check for an existing session when the app loads
  useEffect(() => {
    // Prevent multiple session checks
    if (sessionCheckedRef.current) return;
    sessionCheckedRef.current = true;
    
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      if (session?.user) {
        handleUserUpdate(session.user);
      }
      setIsLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      if (session?.user) {
        handleUserUpdate(session.user);
      } else {
        setUser(null);
      }
    });

    return () => subscription.unsubscribe();
  }, [handleUserUpdate]);

  // Route protection logic moved into its own effect to avoid calling hook inside hook
  const segments = useSegments();
  const router = useRouter();
  const processingRef = React.useRef(false);
  const prevSegmentsRef = React.useRef(segments);
  const prevUserRef = React.useRef(user);

  useEffect(() => {
    // Don't run navigation logic while still loading
    if (isLoading) return;
    
    // Skip if we're already processing to prevent multiple redirects
    if (processingRef.current) {
      return;
    }
    
    // Skip if nothing has changed since last check
    if (
      segments === prevSegmentsRef.current &&
      user === prevUserRef.current
    ) {
      return;
    }
    
    // Update refs
    prevSegmentsRef.current = segments;
    prevUserRef.current = user;
    
    // Check if we're in the auth group
    const inAuthGroup = segments[0] === '(auth)';
    
    // Set processing flag to prevent multiple redirects
    processingRef.current = true;
    
    // IMPORTANT: Add a delay to ensure the root layout is fully mounted
    // This helps prevent the "Attempted to navigate before mounting the Root Layout" error
    const timer = setTimeout(() => {
      try {
        console.log('Attempting navigation based on auth state:', {
          user: !!user,
          inAuthGroup,
          currentSegment: segments[0]
        });
        
        // If user is not signed in and the initial segment is not in the auth group, redirect to login
        if (!user && !inAuthGroup && segments[0] !== 'splash') {
          router.replace('/splash');
        // If user is signed in and the initial segment is in the auth group, redirect to home
        } else if (user && inAuthGroup && segments[0] !== '(tabs)') {
          router.replace('/');
        }
      } catch (navError) {
        console.error('Navigation error in root layout:', navError);
        // If navigation fails, we'll just stay on the current screen
      } finally {
        // Clear the processing flag
        processingRef.current = false;
      }
    }, 300); // Use a longer delay to ensure everything is ready
    
    // Clean up the timer if the component unmounts
    return () => clearTimeout(timer);
  }, [segments, user, router, isLoading]);

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#0078E7" />
        <Text style={{ marginTop: 20, fontSize: 16 }}>Loading...</Text>
      </View>
    );
  }

  // Return a Slot instead of conditional Stack to fix the warning
  return <Slot />;
}
