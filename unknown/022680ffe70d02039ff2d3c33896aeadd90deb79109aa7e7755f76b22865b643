-- Migration: Add Job Recommendation System Tables
-- This migration adds the necessary tables for personalized job recommendations

-- Create user_job_preferences table for personalized filtering
CREATE TABLE IF NOT EXISTS user_job_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  
  -- Location preferences
  preferred_locations TEXT[] DEFAULT '{}',
  max_commute_distance INTEGER DEFAULT 50, -- in miles/km
  remote_work_preference TEXT CHECK (remote_work_preference IN ('required', 'preferred', 'acceptable', 'not_preferred')) DEFAULT 'preferred',
  willing_to_relocate BOOLEAN DEFAULT FALSE,
  
  -- Job type preferences
  preferred_job_types TEXT[] DEFAULT '{}', -- ['Full-time', 'Part-time', 'Contract', 'Freelance']
  preferred_industries TEXT[] DEFAULT '{}',
  preferred_company_sizes TEXT[] DEFAULT '{}', -- ['startup', 'small', 'medium', 'large', 'enterprise']
  
  -- Experience and role preferences
  experience_level TEXT CHECK (experience_level IN ('entry', 'junior', 'mid', 'senior', 'lead', 'executive')) DEFAULT 'mid',
  preferred_roles TEXT[] DEFAULT '{}',
  
  -- Compensation preferences
  min_salary INTEGER,
  max_salary INTEGER,
  salary_currency TEXT DEFAULT 'USD',
  salary_negotiable BOOLEAN DEFAULT TRUE,
  
  -- Work schedule preferences
  preferred_schedule TEXT CHECK (preferred_schedule IN ('flexible', 'standard', 'shift_work', 'weekends_ok')) DEFAULT 'flexible',
  
  -- Preference weights (for scoring algorithm)
  location_weight DECIMAL(3,2) DEFAULT 0.25,
  salary_weight DECIMAL(3,2) DEFAULT 0.30,
  role_weight DECIMAL(3,2) DEFAULT 0.25,
  company_weight DECIMAL(3,2) DEFAULT 0.20,
  
  -- Learning preferences
  auto_learn_from_swipes BOOLEAN DEFAULT TRUE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id)
);

-- Enhance swipes table with additional data for learning algorithm
ALTER TABLE swipes ADD COLUMN IF NOT EXISTS job_title TEXT;
ALTER TABLE swipes ADD COLUMN IF NOT EXISTS job_company TEXT;
ALTER TABLE swipes ADD COLUMN IF NOT EXISTS job_location TEXT;
ALTER TABLE swipes ADD COLUMN IF NOT EXISTS job_salary_min INTEGER;
ALTER TABLE swipes ADD COLUMN IF NOT EXISTS job_salary_max INTEGER;
ALTER TABLE swipes ADD COLUMN IF NOT EXISTS job_type TEXT;
ALTER TABLE swipes ADD COLUMN IF NOT EXISTS job_remote BOOLEAN;
ALTER TABLE swipes ADD COLUMN IF NOT EXISTS job_tags TEXT[];
ALTER TABLE swipes ADD COLUMN IF NOT EXISTS match_score DECIMAL(5,2);

-- Create job_recommendations table for tracking algorithm performance
CREATE TABLE IF NOT EXISTS job_recommendations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  job_id INTEGER REFERENCES jobs(id) ON DELETE CASCADE,
  
  -- Recommendation scoring details
  overall_score DECIMAL(5,2) NOT NULL,
  location_score DECIMAL(5,2),
  salary_score DECIMAL(5,2),
  role_score DECIMAL(5,2),
  company_score DECIMAL(5,2),
  
  -- Recommendation metadata
  algorithm_version TEXT DEFAULT 'v1.0',
  recommendation_reason TEXT,
  
  -- User interaction tracking
  was_viewed BOOLEAN DEFAULT FALSE,
  was_swiped BOOLEAN DEFAULT FALSE,
  swipe_direction TEXT CHECK (swipe_direction IN ('left', 'right')),
  was_applied BOOLEAN DEFAULT FALSE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, job_id)
);

-- Add RLS policies for user_job_preferences
ALTER TABLE user_job_preferences ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own job preferences"
  ON user_job_preferences FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own job preferences"
  ON user_job_preferences FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own job preferences"
  ON user_job_preferences FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own job preferences"
  ON user_job_preferences FOR DELETE
  USING (auth.uid() = user_id);

-- Add RLS policies for job_recommendations
ALTER TABLE job_recommendations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own job recommendations"
  ON job_recommendations FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "System can insert job recommendations"
  ON job_recommendations FOR INSERT
  WITH CHECK (true); -- Allow system to insert recommendations

CREATE POLICY "System can update job recommendations"
  ON job_recommendations FOR UPDATE
  USING (true); -- Allow system to update recommendation tracking

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_job_preferences_user_id ON user_job_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_job_recommendations_user_id ON job_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_job_recommendations_job_id ON job_recommendations(job_id);
CREATE INDEX IF NOT EXISTS idx_job_recommendations_overall_score ON job_recommendations(overall_score DESC);
CREATE INDEX IF NOT EXISTS idx_swipes_user_id_direction ON swipes(user_id, direction);
CREATE INDEX IF NOT EXISTS idx_swipes_job_tags ON swipes USING GIN(job_tags);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_user_job_preferences_updated_at 
  BEFORE UPDATE ON user_job_preferences 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_job_recommendations_updated_at 
  BEFORE UPDATE ON job_recommendations 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert some sample preference data for testing (optional)
-- This can be removed in production
/*
INSERT INTO user_job_preferences (user_id, preferred_locations, preferred_job_types, experience_level, min_salary, max_salary)
SELECT 
  id,
  ARRAY['Remote', 'San Francisco', 'New York'],
  ARRAY['Full-time'],
  'mid',
  80000,
  120000
FROM profiles 
WHERE user_type = 'job_seeker'
ON CONFLICT (user_id) DO NOTHING;
*/

-- Migration complete
SELECT 'Job recommendation system tables created successfully' as status;
