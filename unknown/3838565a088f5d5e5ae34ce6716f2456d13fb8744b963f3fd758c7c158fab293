<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/@react-native/gradle-plugin" />
        <option name="gradleJvm" value="#JAVA_HOME" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$/Jobbify/node_modules/@react-native/gradle-plugin" />
            <option value="$PROJECT_DIR$/Jobbify/node_modules/@react-native/gradle-plugin/react-native-gradle-plugin" />
            <option value="$PROJECT_DIR$/Jobbify/node_modules/@react-native/gradle-plugin/settings-plugin" />
            <option value="$PROJECT_DIR$/Jobbify/node_modules/@react-native/gradle-plugin/shared" />
            <option value="$PROJECT_DIR$/Jobbify/node_modules/@react-native/gradle-plugin/shared-testutil" />
          </set>
        </option>
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-application/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-asset/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-av/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-blur/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-constants/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-constants/scripts" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-crypto/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-document-picker/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-file-system/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-font/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-keep-awake/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-linear-gradient/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-linking/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-modules-autolinking/scripts/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-modules-core/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-speech/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-splash-screen/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo-web-browser/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/expo/scripts" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/react-native" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/react-native-gesture-handler/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/react-native-reanimated/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/react-native-safe-area-context/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/react-native-screens/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/Jobbify/node_modules/react-native-svg/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/@react-native/gradle-plugin" />
        <option name="gradleJvm" value="#JAVA_HOME" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$/node_modules/@react-native/gradle-plugin" />
            <option value="$PROJECT_DIR$/node_modules/@react-native/gradle-plugin/react-native-gradle-plugin" />
            <option value="$PROJECT_DIR$/node_modules/@react-native/gradle-plugin/settings-plugin" />
            <option value="$PROJECT_DIR$/node_modules/@react-native/gradle-plugin/shared" />
            <option value="$PROJECT_DIR$/node_modules/@react-native/gradle-plugin/shared-testutil" />
          </set>
        </option>
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-application/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-asset/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-blur/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-constants/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-constants/scripts" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-crypto/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-document-picker/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-file-system/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-font/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-keep-awake/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-linear-gradient/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-linking/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-modules-autolinking/scripts/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-modules-core/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-splash-screen/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-web-browser/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo/scripts" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/react-native" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/react-native-gesture-handler/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/react-native-reanimated/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/react-native-safe-area-context/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/react-native-screens/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/react-native-svg/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
    </option>
  </component>
</project>