name: <PERSON><PERSON> Warming Cron Job

on:
  schedule:
    # Run every 4 hours at 02:05, 06:05, 10:05, 14:05, 18:05, 22:05 UTC
    - cron: '5 2,6,10,14,18,22 * * *'
  workflow_dispatch: # Allow manual triggering

jobs:
  warm-cache:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        cd job-api
        pip install -r requirements.txt
        
    - name: Install Redis
      run: |
        sudo apt-get update
        sudo apt-get install -y redis-server
        sudo systemctl start redis-server
        
    - name: Run cache warming
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_KEY: ${{ secrets.SUPABASE_KEY }}
        REDIS_URL: redis://localhost:6379
        JOOBLE_API_KEY: ${{ secrets.JOOBLE_API_KEY }}
        ADZUNA_APP_ID: ${{ secrets.ADZUNA_APP_ID }}
        ADZUNA_APP_KEY: ${{ secrets.ADZUNA_APP_KEY }}
      run: |
        cd job-api
        python warm_cache.py
        
    - name: Notify on failure
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: 'Cache warming job failed!'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
