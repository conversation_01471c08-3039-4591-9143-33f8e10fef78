#!/bin/bash

echo "Starting Redis server for development..."
echo

# Check if Redis is installed
if ! command -v redis-server &> /dev/null; then
    echo "Redis is not installed."
    echo
    echo "To install Redis:"
    echo "  Ubuntu/Debian: sudo apt install redis-server"
    echo "  macOS: brew install redis"
    echo "  Or use Docker: docker run -d -p 6379:6379 redis:alpine"
    echo
    exit 1
fi

echo "Starting Redis server on port 6379..."
redis-server --port 6379 --save 60 1 --loglevel notice
