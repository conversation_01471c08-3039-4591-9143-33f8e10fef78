global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  slack_api_url: 'YOUR_SLACK_WEBHOOK_URL_HERE'

route:
  group_by: ['alertname', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
    - match:
        severity: critical
      receiver: 'slack-critical'
    - match:
        severity: warning
      receiver: 'slack-warnings'
    - match:
        service: job-api
      receiver: 'slack-job-api'

receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://127.0.0.1:5001/'

  - name: 'slack-critical'
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL_HERE'
        channel: '#alerts'
        title: '🚨 CRITICAL ALERT: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Service:* {{ .Labels.service }}
          {{ if .Labels.api }}*API:* {{ .Labels.api }}{{ end }}
          {{ if .Annotations.runbook_url }}*Runbook:* {{ .Annotations.runbook_url }}{{ end }}
          {{ end }}
        color: 'danger'
        send_resolved: true

  - name: 'slack-warnings'
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL_HERE'
        channel: '#alerts'
        title: '⚠️ WARNING: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Service:* {{ .Labels.service }}
          {{ if .Labels.api }}*API:* {{ .Labels.api }}{{ end }}
          {{ end }}
        color: 'warning'
        send_resolved: true

  - name: 'slack-job-api'
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL_HERE'
        channel: '#job-api-alerts'
        title: '📊 Job API Alert: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          {{ if .Labels.component }}*Component:* {{ .Labels.component }}{{ end }}
          {{ if .Labels.api }}*API:* {{ .Labels.api }}{{ end }}
          {{ end }}
        send_resolved: true

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'service']
