{"version": 3, "names": ["_react", "require", "_external", "useBottomSheet", "context", "useContext", "BottomSheetContext", "exports"], "sourceRoot": "../../../src", "sources": ["hooks/useBottomSheet.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AAEO,MAAME,cAAc,GAAGA,CAAA,KAAM;EAClC,MAAMC,OAAO,GAAG,IAAAC,iBAAU,EAACC,4BAAkB,CAAC;EAE9C,IAAIF,OAAO,KAAK,IAAI,EAAE;IACpB,MAAM,yDAAyD;EACjE;EAEA,OAAOA,OAAO;AAChB,CAAC;AAACG,OAAA,CAAAJ,cAAA,GAAAA,cAAA", "ignoreList": []}