# muse_service.py
import os
import asyncio
import httpx
from typing import List, Dict, Any

# The Muse API configuration
MUSE_API_KEY = "39a8dde5f23991b29f8097f3638271072d9a749c44ee7f08de7f5b7e404b72d2"
MUSE_BASE_URL = "https://www.themuse.com/api/public"

async def muse(client: httpx.AsyncClient, category: str = "Computer and IT", location: str = "Flexible / Remote") -> List[Dict[str, Any]]:
    """Fetch jobs from The Muse API"""
    try:
        print(f"🔄 Fetching from The Muse API with category: {category}, location: {location}...")
        
        headers = {
            "User-Agent": "JobbifyBot/1.0 (+https://jobbify.app)",
            "X-API-Key": MUSE_API_KEY
        }
        
        params = {
            "category": category,
            "location": location,
            "page": 0,
            "descending": "true",
            "api_key": MUSE_API_KEY
        }
        
        url = f"{MUSE_BASE_URL}/jobs"
        
        r = await client.get(url, 
                            params=params,
                            headers=headers, 
                            timeout=20)
        r.raise_for_status()
        
        data = r.json()
        jobs = data.get("results", [])
        
        # Add source and external_id for each job
        for job in jobs:
            job["source"] = "muse"
            job["external_id"] = str(job.get("id", ""))
            
        print(f"✅ The Muse: Found {len(jobs)} jobs")
        return jobs
    except Exception as e:
        print(f"❌ The Muse error: {e}")
        return []

def map_muse_job(j: Dict[str, Any]) -> Dict[str, Any]:
    """Map The Muse job data to standardized format"""
    try:
        # Extract company information
        company_info = j.get("company", {})
        company_name = company_info.get("name", "Unknown Company")
        
        # Extract location information
        locations = j.get("locations", [])
        location = "Remote"
        if locations and len(locations) > 0:
            location = locations[0].get("name", "Remote")
        
        # Extract categories for tags
        categories = j.get("categories", [])
        category_names = [cat.get("name", "") for cat in categories if cat.get("name")]
        
        payload = {
            "title": j.get("name", ""),
            "company": company_name,
            "location": location,
            "salary": "Competitive",  # The Muse doesn't always provide salary info
            "logo": company_info.get("logo", ""),
            "apply_url": j.get("refs", {}).get("landing_page", ""),
            "description": j.get("contents", "")[:1000],  # The Muse uses 'contents' for description
            "source": "muse",
            "external_id": str(j.get("id", "")),
        }
        return payload
    except Exception as e:
        print(f"Error mapping The Muse job: {e}")
        return {
            "title": j.get("name", "Unknown Job"),
            "company": "Unknown Company",
            "description": "The Muse job data could not be properly parsed.",
            "source": "muse",
            "external_id": str(j.get("id", "")),
        }
